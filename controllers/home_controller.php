<?php
// Contrôleur pour la page d'accueil

/**
 * Page d'accueil
 */
function home_index() {
    $data = [
        'title' => 'Accueil',
        'message' => 'Bienvenue sur Ⓜ️édiaBox, la médiathèque en ligne !',
        'features' => [
            'Catalogue 📔' => "index",
            'Jeux vidéos 🎮' => "games",
            'Livres 📚'=> "books",
            'Films 🎬' => "movies"
    ]
    ];

    load_view_with_layout('home/index', $data);
}

/**
 * Page à propos
 */
function home_about() {
    $data = [
        'title' => 'À propos',
        'content' => 'Cette application est un starter kit PHP MVC développé avec une approche procédurale.'
    ];
    
    load_view_with_layout('home/about', $data);
}



/**
 * Page contact
 */
function home_contact() {
    $data = [
        'title' => 'Contact'
    ];
    
    if (is_post()) {
        $name = clean_input(post('name'));
        $email = clean_input(post('email'));
        $message = clean_input(post('message'));
        
        // Validation simple
        if (empty($name) || empty($email) || empty($message)) {
            set_flash('error', 'Tous les champs sont obligatoires.');
        } elseif (!validate_email($email)) {
            set_flash('error', 'Adresse email invalide.');
        } else {
            // Ici vous pourriez envoyer l'email ou sauvegarder en base
            set_flash('success', 'Votre message a été envoyé avec succès !');
            redirect('home/contact');
        }
    }
    
    load_view_with_layout('home/contact', $data);
} 


/**
 * Page profile
 */
function home_profile() {
    $data = [
        'title' => 'Profile',
        'message' => 'Bienvenue sur votre profil',
        'content' => 'Cette application est un starter kit PHP MVC développé avec une approche procédurale.'
    ];
    
    load_view_with_layout('home/profile', $data);
} 



/**
 * Page test
 */
function home_test() {
    $data = [
        'title' => 'Page test',
        'message' => 'Bienvenue sur votre page test',
    ];
    
    load_view_with_layout('home/test', $data);
} 
//fonction ajoute par Nicolas
/* function home_inscription() {
    $erreurs = [];

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $nom = ucfirst(trim($_POST['nom'] ?? ''));
        $prenom = ucfirst(trim($_POST['prenom'] ?? ''));
        $email = strtolower(trim($_POST['email'] ?? ''));
        $mot_de_passe = $_POST['mot_de_passe'] ?? '';
        $confirmation = $_POST['confirmation_mot_de_passe'] ?? '';

        // --- VALIDATION ---
        if (strlen($nom) < 2 || strlen($nom) > 50 || !preg_match('/^[A-Za-zÀ-ÿ\- ]+$/', $nom)) {
            $erreurs[] = "Nom invalide.";
        }

        if (strlen($prenom) < 2 || strlen($prenom) > 50 || !preg_match('/^[A-Za-zÀ-ÿ\- ]+$/', $prenom)) {
            $erreurs[] = "Prénom invalide.";
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL) || strlen($email) > 100) {
            $erreurs[] = "Email invalide.";
        }

        if ($mot_de_passe !== $confirmation) {
            $erreurs[] = "Les mots de passe ne correspondent pas.";
        }

        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/', $mot_de_passe)) {
            $erreurs[] = "Mot de passe trop faible.";
        }

        // Vérifier si l'email est déjà en base
        $userModel = new UserModel();
        if ($userModel->emailExiste($email)) {
            $erreurs[] = "L'email est déjà utilisé.";
        }

        if (empty($erreurs)) {
            $hash = password_hash($mot_de_passe, PASSWORD_DEFAULT);
            $userModel->ajouterUtilisateur($nom, $prenom, $email, $hash);
            set_flash('success', "Inscription réussie !");
            redirect('home/connexion'); 
        }
    }

    $data = [
        'title' => 'Inscription',
        'erreurs' => $erreurs
    ];

    load_view_with_layout('home/inscription', $data);
}
 */