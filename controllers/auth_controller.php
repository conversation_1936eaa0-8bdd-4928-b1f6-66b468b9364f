<?php
// Contrôleur d'authentification

/**
 * Page de connexion
 */
function auth_login()
{

    // Rediriger si déjà connecté
    if (is_logged_in()) {
        redirect('home');
    }

    $data = [
        'title' => 'Connexion'
    ];

    if (is_post()) {
        $email = clean_input(post('email'));
        $password = post('password');

        if (empty($email) || empty($password)) {
            set_flash('error', 'Email et mot de passe obligatoires.');
        } else {
            // Rechercher l'utilisateur
            if ($email == '<EMAIL>') {
                $user = get_admin_by_email($email);
            } else {
                $user = get_user_by_email($email);
            }

            if ($user && verify_password($password, $user['password'])) {
                // Connexion réussie
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_role'] = $user['role'];
                set_flash('success', 'Connexion réussie !');
                redirect('home');
            } else {
                set_flash('error', 'Email ou mot de passe incorrect.');
            }
        }
    }

    load_view_with_layout('auth/login', $data);
}

/**
 * Page d'inscription
 */
function auth_register()
{
    // Rediriger si déjà connecté
    if (is_logged_in()) {
        redirect('home');
    }

    $data = [
        'title' => 'Inscription'
    ];

    if (is_post()) {
        $first_name = clean_input(post('first_name'));
        $last_name = clean_input(post('last_name'));
        $email = clean_input(post('email'));
        $password = post('password');
        $confirm_password = post('confirm_password');

        // Validation
        if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
            set_flash('error', 'Tous les champs sont obligatoires.');
        } elseif (!validate_email($email)) {
            set_flash('error', 'Adresse email invalide.');
        } elseif (strlen($password) < 6) {
            set_flash('error', 'Le mot de passe doit contenir au moins 6 caractères.');
        } elseif ($password !== $confirm_password) {
            set_flash('error', 'Les mots de passe ne correspondent pas.');
        } elseif (get_user_by_email($email)) {
            set_flash('error', 'Cette adresse email est déjà utilisée.');
        } else {
            // Créer l'utilisateur
            $user_id = create_user($first_name, $last_name, $email, $password);

            if ($user_id) {
                set_flash('success', 'Inscription réussie ! Vous pouvez maintenant vous connecter.');
                redirect('auth/login');
            } else {
                set_flash('error', 'Erreur lors de l\'inscription.');
            }
        }
    }

    load_view_with_layout('auth/register', $data);
}

/**
 * Déconnexion
 */
function auth_logout()
{
    logout();
}
