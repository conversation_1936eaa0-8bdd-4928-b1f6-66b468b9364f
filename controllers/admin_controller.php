<?php


/* 
* Tableau de bord adminstrateur
*/
function admin_index()
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();

    $all_users = get_all_users();

    $data = [
        'styles' => 'admin',
        'title' => 'Interface d\'administration',
        'users' => $all_users
    ];

    load_view_with_layout('admin/index', $data);
}


function admin_dashboard()
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();

    $number_of_medias_by_type = get_number_of_medias_by_type();

    $data = [
        'styles' => 'admin',
        'title' => 'Tableau de bord - Administration',
        'number_of_medias_by_type' => $number_of_medias_by_type
    ];

    load_view_with_layout('admin/dashboard', $data);
}

function admin_return_loan($loan_id)
{
    if (!$loan_id) {
        set_flash('error', 'ID d\'emprunt introuvable');
        redirect('admin');
        return;
    }

    $loan = get_loan_by_id($loan_id);
    if (!$loan) {
        set_flash('error', 'Emprunt introuvable');
        redirect('admin');
        return;
    }

    // Mettre à jour le statut de l'emprunt
    $returned = update_loan_status($loan_id, 'Retourné');

    if ($returned) {
        set_flash('success', 'Emprunt retourné avec succès !');
        // Rediriger vers la page de détails de l'utilisateur
        redirect('admin/user_details/' . $loan['user_id']);
    } else {
        set_flash('error', 'Erreur lors du retour de l\'emprunt.');
        redirect('admin');
    }
}

function admin_user_details($user_id)
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();


    if (!$user_id) {
        set_flash('error', 'ID utilisateur introuvable');
        redirect('admin');
        return;
    }

    $user = get_user_by_id($user_id);
    if (!$user) {
        set_flash('error', 'Utilisateur introuvable');
        redirect('admin');
        return;
    }

    // Récupérer les emprunts de l'utilisateur
    $user_loans = get_user_active_loans($user_id);

    $user_loan_history = get_user_loan_history($user_id);

    $stats = get_user_statistics($user_id);

    $data = [
        'styles' => 'admin',
        'title' => 'Détails utilisateur - ' . $user['first_name'] . ' ' . $user['last_name'],
        'user' => $user,
        'active_loans' => $user_loans,
        'loan_history' => $user_loan_history,
        'stats' => $stats
    ];

    load_view_with_layout('admin/user_details', $data);
}


/* 
* Modifier un utilisateur 
*/
function admin_edit($id)
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();
    if (!$id) {
        set_flash('error', 'ID utilisateur introuvable');
        redirect('admin');
        return;
    }

    /* 
    * recuperer l'utilisateur 
    */
    $user = get_user_by_id($id);
    if (!$user) {
        set_flash('error', 'Utilisateur introuvable');
        redirect('admin');
        return;
    }

    if (is_post()) {
        $first_name = clean_input(post('first_name'));
        $last_name = clean_input(post('last_name'));
        $email = clean_input(post('email'));
        $password = post('password');

        // Validation des champs
        if (empty($first_name) || empty($last_name) || empty($email)) {
            set_flash('error', 'Tous les champs sont obligatoires.');
        } elseif (!validate_email($email)) {
            set_flash('error', 'Adresse email invalide.');
        } elseif (strlen($password) < 6) {
            set_flash('error', 'Le mot de passe doit contenir au moins 6 caractères.');
        } else {
            update_user($id, $first_name, $last_name, $email, $password);
            set_flash('success', 'Utilisateur modifié avec succès !');
            redirect('admin');
        }
    }
}

// ============ Gestion des médias ============

/**
 * Page de gestion des médias
 */
function admin_medias()
{
    has_admin_privileges();
    $search = clean_input(get('search', ''));
    $type = clean_input(get('type', ''));
    $genre = clean_input(get('genre', ''));
    $page = (int)get('page', 1);
    $limit = 20;
    $offset = ($page - 1) * $limit;

    $medias = admin_get_all_medias($limit, $offset, $search, $type, $genre);
    $total_medias = count_medias($search, $type, $genre);
    $total_pages = ceil($total_medias / $limit);

    $genres = get_all_genres();

    $data = [
        'styles' => 'admin',
        'title' => 'Gestion des médias',
        'medias' => $medias,
        'genres' => $genres,
        'search' => $search,
        'selected_type' => $type,
        'selected_genre' => $genre,
        'current_page' => $page,
        'total_pages' => $total_pages,
        'total_medias' => $total_medias
    ];

    load_view_with_layout('admin/medias', $data);
}

/**
 * Page d'ajout d'un média
 */
function admin_add_media()
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();
    $genres = get_all_genres();
    $platforms = get_all_platforms();
    $classifications = get_all_classifications();

    $data = [
        'styles' => 'admin',
        'title' => 'Ajouter un média',
        'genres' => $genres,
        'platforms' => $platforms,
        'classifications' => $classifications
    ];

    // Helper function for validation
    $fields_errors = [];
    $validate_field = function ($field_name, $value, $is_numeric = false) use (&$fields_errors) {
        if ($is_numeric) {
            // For numeric fields, check if the value is <= 0 (which includes empty converted to 0)
            if ($value <= 0) {
                $fields_errors[] = $field_name;
            }
        } else {
            if (empty($value)) {
                $fields_errors[] = $field_name;
            }
        }
    };

    if (is_post()) {
        // If this is just a media type selection, don't validate the full form
        if (post('select_type')) {
            // Just reload the form with the selected media type, no validation needed
            load_view_with_layout('admin/add_media', $data);
            return;
        }

        // Données communes
        $title = clean_input(post('title'));
        $media_type = clean_input(post('media_type'));
        $genre_name = (int)post('genre');
        $stock = (int)post('stock');
        $cover = clean_input(post('cover'));

        // Validation des champs communs
        $validate_field('title', $title);
        $validate_field('media_type', $media_type);
        $validate_field('genre_name', $genre_name);
        $validate_field('stock', $stock, true);
        $validate_field('cover', $cover);

        // Vérification des erreurs de validation et les afficher avec chaque champ
        if (!empty($fields_errors)) {
            set_flash('error', 'Veuillez remplir tous les champs requis : ' . implode(', ', $fields_errors) . '.');
            load_view_with_layout('admin/add_media', $data);
            return;
        }

        // Données spécifiques selon le type
        $specific_data = [];
        $valid = true;

        switch ($media_type) {
            case 'book':
                $specific_data = [
                    'author' => clean_input(post('author')),
                    'isbn' => clean_input(post('isbn')),
                    'pages' => (int)post('pages'),
                    'summary' => clean_input(post('summary')),
                    'year' => (int)post('year')
                ];

                // Reset errors for specific validation
                $fields_errors = [];
                $validate_field('author', $specific_data['author']);
                $validate_field('isbn', $specific_data['isbn']);
                $validate_field('pages', $specific_data['pages'], true);
                $validate_field('summary', $specific_data['summary']);
                $validate_field('year', $specific_data['year'], true);

                if (!empty($fields_errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le livre : ' . implode(', ', $fields_errors) . '.');
                    $valid = false;
                } elseif (isbn_exists($specific_data['isbn'])) {
                    set_flash('error', 'Cet ISBN existe déjà.');
                    $valid = false;
                }
                break;

            case 'film':
                $specific_data = [
                    'director' => clean_input(post('director')),
                    'year' => (int)post('year'),
                    'duration' => (int)post('duration'),
                    'synopsis' => clean_input(post('synopsis')),
                    'classification_id' => (int)post('classification_id')
                ];

                // Reset errors for specific validation
                $fields_errors = [];
                $validate_field('director', $specific_data['director']);
                $validate_field('year', $specific_data['year'], true);
                $validate_field('duration', $specific_data['duration'], true);
                $validate_field('synopsis', $specific_data['synopsis']);
                $validate_field('classification_id', $specific_data['classification_id'], true);

                if (!empty($fields_errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le film : ' . implode(', ', $fields_errors) . '.');
                    $valid = false;
                }
                break;

            case 'game':
                $specific_data = [
                    'publisher' => clean_input(post('publisher')),
                    'platform_id' => (int)post('platform_id'),
                    'minimum_age' => (int)post('minimum_age'),
                    'description' => clean_input(post('description')),
                    'year' => (int)post('year')
                ];

                // Reset errors for specific validation
                $fields_errors = [];
                $validate_field('publisher', $specific_data['publisher']);
                $validate_field('platform_id', $specific_data['platform_id'], true);
                $validate_field('minimum_age', $specific_data['minimum_age'], true);
                $validate_field('description', $specific_data['description']);
                $validate_field('year', $specific_data['year'], true);

                if (!empty($fields_errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le jeu : ' . implode(', ', $fields_errors) . '.');
                    $valid = false;
                }
                break;

            default:
                set_flash('error', 'Type de média invalide.');
                $valid = false;
        }

        if ($valid && create_media($title, $media_type, $genre_name, $stock, $cover, $specific_data)) {
            set_flash('success', 'Média ajouté avec succès !');
            redirect('admin/medias');
        } else if ($valid) {
            set_flash('error', 'Erreur lors de l\'ajout du média.');
        }
    }

    load_view_with_layout('admin/add_media', $data);
}

/**
 * Page d'édition d'un média
 */
function admin_edit_media($id)
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();
    if (!$id) {
        set_flash('error', 'ID média introuvable');
        redirect('admin/medias');
        return;
    }

    $media = get_media_by_id($id);
    if (!$media) {
        set_flash('error', 'Média introuvable');
        redirect('admin/medias');
        return;
    }

    $genres = get_all_genres();
    $platforms = get_all_platforms();
    $classifications = get_all_classifications();

    $data = [
        'styles' => 'admin',
        'title' => 'Modifier le média - ' . $media['title'],
        'media' => $media,
        'genres' => $genres,
        'platforms' => $platforms,
        'classifications' => $classifications
    ];

    if (is_post()) {
        // Données communes
        $title = clean_input(post('title'));
        $genre_name = (int)post('genre_name');
        $stock = (int)post('stock');
        $cover = clean_input(post('cover'));

        // Validation des champs communs
        if (empty($title) || $stock <= 0 || empty($cover)) {
            set_flash('error', 'Veuillez remplir tous les champs requis.');
            load_view_with_layout('admin/edit_media', $data);
            return;
        }

        // Données spécifiques selon le type
        $specific_data = [];
        $valid = true;

        switch ($media['media_type']) {
            case 'book':
                $specific_data = [
                    'author' => clean_input(post('author')),
                    'isbn' => clean_input(post('isbn')),
                    'pages' => (int)post('pages'),
                    'summary' => clean_input(post('summary')),
                    'year' => (int)post('year')
                ];

                $errors = [];
                foreach ($specific_data as $key => $value) {
                    if (empty($value) && in_array($key, ['author', 'isbn', 'year', 'pages', 'summary', 'year'])) {
                        $errors[] = $key;
                    }
                };
                if (!empty($errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le livre : ' . implode(', ', $errors));
                    $valid = false;
                } elseif (isbn_exists($specific_data['isbn'], $id)) {
                    set_flash('error', 'Cet ISBN existe déjà.');
                    $valid = false;
                }
                break;

            case 'film':
                $specific_data = [
                    'director' => clean_input(post('director')),
                    'year' => (int)post('year'),
                    'duration' => (int)post('duration'),
                    'synopsis' => clean_input(post('synopsis')),
                    'classification_id' => (int)post('classification_id')
                ];

                $errors = [];
                foreach ($specific_data as $key => $value) {
                    if (empty($value) && in_array($key, ['director', 'year', 'duration', 'synopsis', 'classification_id'])) {
                        $errors[] = $key;
                    }
                };
                if (!empty($errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le film : ' . implode(', ', $errors));
                    $valid = false;
                }
                break;

            case 'game':
                $specific_data = [
                    'publisher' => clean_input(post('publisher')),
                    'platform_id' => (int)post('platform_id'),
                    'minimum_age' => (int)post('minimum_age'),
                    'description' => clean_input(post('description')),
                    'year' => (int)post('year')
                ];
                $errors = [];
                foreach ($specific_data as $key => $value) {
                    if (empty($value) && in_array($key, ['publisher', 'year', 'minimum_age', 'description'])) {
                        $errors[] = $key;
                    }
                };
                if (!empty($errors)) {
                    set_flash('error', 'Veuillez remplir tous les champs requis pour le jeu : ' . implode(', ', $errors));
                    $valid = false;
                }
                break;
        }

        if ($valid && update_media($id, $title, $genre_name, $stock, $specific_data)) {
            set_flash('success', 'Média modifié avec succès !');
            redirect('admin/medias');
        } else if ($valid) {
            set_flash('error', 'Erreur lors de la modification du média.');
        }
    }

    load_view_with_layout('admin/edit_media', $data);
}

/**
 * Supprimer un média
 */
function admin_delete_media($id)
{
    // Vérifier si l'utilisateur est connecté et est un administrateur
    has_admin_privileges();
    if (!$id) {
        set_flash('error', 'ID média introuvable');
        redirect('admin/medias');
        return;
    }

    $media = get_media_by_id($id);
    if (!$media) {
        set_flash('error', 'Média introuvable');
        redirect('admin/medias');
        return;
    }

    if (is_post() && post('confirm') === 'yes') {
        if (delete_media($id)) {
            set_flash('success', 'Média supprimé avec succès !');
        } else {
            set_flash('error', 'Impossible de supprimer ce média. Il est peut-être encore emprunté.');
        }
        redirect('admin/medias');
    }

    $data = [
        'styles' => 'admin',
        'title' => 'Supprimer le média',
        'media' => $media
    ];

    load_view_with_layout('admin/delete_media', $data);
}



/*
    * function pour la securité et la verfication d'admin
    */
function has_admin_privileges()
{
    if (!is_admin()) {
        redirect('not_found');
    }
}
