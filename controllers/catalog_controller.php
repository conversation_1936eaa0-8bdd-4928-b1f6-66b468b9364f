<?php

function catalog_index() {
    $medias = get_all_medias(); 

    $data = [
        'styles' => 'catalog',
        'title' => 'Catalogue',
        'medias' => $medias,
        'selected_title' => '',
        'selected_type' => '',
        'selected_genre' => '',
        'selected_disponibilite' => '',
    ];

    load_view_with_layout('catalog/index', $data);
}


function catalog_search() {
    $filters = [
        'titre' => get('titre'),
        'type' => get('type'),
        'genre' => get('genre'),
        'disponibilite' => get('disponibilite'),
    ];

    $medias = search_medias($filters);

    $data = [
        'styles' => 'catalog',
        'title' => 'Résultats de la recherche',
        'medias' => $medias,
        'selected_title' => $filters['titre'] ?? '',
        'selected_type' => $filters['type'] ?? '',
        'selected_genre' => $filters['genre'] ?? '',
        'selected_disponibilite' => $filters['disponibilite'] ?? '',
    ];

    load_view_with_layout('catalog/index', $data);

}


function catalog_detail() {
    $id = $_GET['id'] ?? null;
    if (!$id || !is_numeric($id)) {
        header('Location: ' . url('catalog/index'));
        exit;
    }

    $media = media_detail((int)$id);

    if (!$media) {
        header('HTTP/1.0 404 Not Found');
        echo "Média introuvable";
        exit;
    }
    $search = $_GET['titre'] ?? '';
    $filter_type = $_GET['type'] ?? '';
    $filter_genre = $_GET['genre'] ?? '';
    $filter_disponibilite = $_GET['disponibilite'] ?? '';

    $data = [
        'title' => 'Détail du média',
        'styles' => 'catalog',
        'media' => $media,
        'search' => $search,
        'filter_type' => $filter_type,
        'filter_genre' => $filter_genre,
        'filter_disponibilite' => $filter_disponibilite,
    ];

    load_view_with_layout('catalog/media_detail', $data);
}


// Je travaille ici 


