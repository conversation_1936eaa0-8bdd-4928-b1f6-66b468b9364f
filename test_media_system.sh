#!/bin/bash

# Script de test pour le système de gestion des médias

echo "🚀 Test du Système de Gestion des Médias..."

# URL de base pour l'application
BASE_URL="http://localhost/la_plateforme/mediatheque_gr2_marseille/public"

echo "📋 Test d'accessibilité de l'application..."

# Tester si la page principale est accessible
response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL")
if [ $response == "200" ]; then
    echo "✅ Page principale accessible"
else
    echo "❌ Page principale non accessible (HTTP $response)"
fi

# Tester les pages admin
echo "📊 Test des pages admin..."

admin_pages=(
    "contact"
    "admin/medias"
    "admin/add_media"
)

for page in "${admin_pages[@]}"; do
    response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/index.php?url=$page")
    if [ $response == "200" ] || [ $response == "302" ]; then
        echo "✅ $page accessible"
    else
        echo "❌ $page non accessible (HTTP $response)"
    fi
done

echo "🎯 Test terminé !"
echo ""
echo "📝 Prochaines étapes :"
echo "1. Accéder à $BASE_URL/index.php?url=admin/medias pour voir l'interface de gestion des médias"
echo "2. Essayer d'ajouter un nouveau média avec $BASE_URL/index.php?url=admin/add_media"
echo "3. Tester la modification et la suppression de médias"
echo ""
echo "🔧 Fonctions admin disponibles :"
echo "- Voir tous les médias avec filtres et recherche"
echo "- Ajouter de nouveaux livres, films et jeux"
echo "- Modifier les médias existants"
echo "- Supprimer des médias (avec confirmation)"
echo "- Validation des champs obligatoires"
echo "- Validation d'unicité ISBN pour les livres"
echo "- Gestion des stocks"
