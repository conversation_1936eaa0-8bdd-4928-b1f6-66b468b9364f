<?php

// Récupérer le nombre de médias par catégorie
function get_number_of_medias_by_type()
{
    $query = "
    SELECT m.media_type, COUNT(*) AS count FROM medias m
    GROUP BY m.media_type
    ";

    return db_select($query);
}



function get_user_active_loans($user_id)
{
    $query = "
    SELECT 
    m.title AS media_title,
    m.media_type,
    l.id,
    l.loan_date,
    l.due_date,
    l.status
    FROM loans l
    JOIN medias m ON l.media_id = m.id
    WHERE l.user_id = ? AND l.status = 'En cours'
    ORDER BY l.loan_date DESC
    ";

    return db_select($query, [$user_id]);
}


function count_user_active_loans($user_id)
{
    $query = "
    SELECT COUNT(*) AS count
    FROM loans
    WHERE user_id = ? AND status = 'En cours'
    ";
    $result = db_select($query, [$user_id]);

    if (is_array($result) && isset($result[0]['count'])) {
        return $result[0]['count'];
    }
    return 0;
}


/**
 * Récupérer un emprunt par son ID
 */
function get_loan_by_id($loan_id)
{
    $query = "
    SELECT 
    l.id,
    l.user_id,
    l.media_id,
    l.loan_date,
    l.due_date,
    l.status
    FROM loans l
    WHERE l.id = ?
    ";

    return db_select_one($query, [$loan_id]);
}

/**
 * Mettre à jour le statut d'un emprunt
 */

function update_loan_status($loan_id, $status)
{
    $query = "UPDATE loans SET status = ? WHERE id = ?";
    return db_execute($query, [$status, $loan_id]);
}


/**
 * Récupérer l'historique des emprunts d'un utilisateur
 */

function get_user_loan_history($user_id)
{
    $query = "
    SELECT 
    m.title AS media_title,
    m.media_type,
    l.loan_date,
    l.due_date,
    l.status
    FROM loans l
    JOIN medias m ON l.media_id = m.id
    WHERE l.user_id = ?
    AND l.status != 'En cours'
    ORDER BY l.loan_date DESC
    ";

    return db_select($query, [$user_id]);
}


/**
 * Récupérer les statistiques d'un utilisateur
 */
function get_user_statistics($user_id)
{

    $query = "SELECT COUNT(*) AS total FROM loans WHERE user_id = ?";
    $result = db_select_one($query, [$user_id]);
    $stats['total_loans'] = $result['total'] ?? 0;

    $stats['active_loans'] = count_user_active_loans($user_id);

    $query = "SELECT COUNT(*) AS count FROM loans WHERE user_id = ? AND status = 'En cours' AND due_date < NOW()";
    $result = db_select_one($query, [$user_id]);
    $stats['overdue_loans'] = $result['count'] ?? 0;

    $query = "SELECT m.media_type, COUNT(*) AS count FROM loans l JOIN medias m ON l.media_id = m.id WHERE l.user_id = ? GROUP BY m.media_type ORDER BY count DESC LIMIT 1";
    $result = db_select_one($query, [$user_id]);
    $stats['favorite_type'] = $result['media_type'] ?? 'Aucun';
    return $stats;
}

// ============ MEDIA MANAGEMENT FUNCTIONS ============

/**
 * Récupère tous les médias avec leurs informations
 */
function admin_get_all_medias($limit = null, $offset = 0, $search = '', $type = '', $genre = '')
{
    $query = "
    SELECT 
        m.id, 
        m.title, 
        m.media_type, 
        m.stock,
        g.name AS genre_name,
        m.created_at
    FROM medias m
    LEFT JOIN genres g ON m.genre_id = g.id
    WHERE 1=1
    ";

    $params = [];

    if (!empty($search)) {
        $query .= " AND m.title LIKE ?";
        $params[] = '%' . $search . '%';
    }

    if (!empty($type)) {
        $query .= " AND m.media_type = ?";
        $params[] = $type;
    }

    if (!empty($genre)) {
        $query .= " AND m.genre_id = ?";
        $params[] = $genre;
    }

    $query .= " ORDER BY m.created_at DESC";

    if ($limit !== null) {
        $query .= " LIMIT $offset, $limit";
    }

    return db_select($query, $params);
}

/**
 * Compte le nombre total de médias
 */
function count_medias($search = '', $type = '', $genre = '')
{
    $query = "SELECT COUNT(*) as count FROM medias m WHERE 1=1";
    $params = [];

    if (!empty($search)) {
        $query .= " AND m.title LIKE ?";
        $params[] = '%' . $search . '%';
    }

    if (!empty($type)) {
        $query .= " AND m.media_type = ?";
        $params[] = $type;
    }

    if (!empty($genre)) {
        $query .= " AND m.genre_id = ?";
        $params[] = $genre;
    }

    $result = db_select_one($query, $params);
    return $result['count'] ?? 0;
}

/**
 * Récupère un média par son ID avec tous ses détails
 */
function get_media_by_id($id)
{
    $query = "
    SELECT 
        m.id, 
        m.title, 
        m.media_type, 
        m.genre_id,
        m.stock,
        m.images AS cover,
        m.created_at,
        g.name AS genre_name
    FROM medias m
    LEFT JOIN genres g ON m.genre_id = g.id
    WHERE m.id = ?
    ";

    $media = db_select_one($query, [$id]);

    if ($media) {
        // Récupérer les détails spécifiques selon le type
        switch ($media['media_type']) {
            case 'book':
                $book_query = "SELECT * FROM books WHERE media_id = ?";
                $book_details = db_select_one($book_query, [$id]);
                $media = array_merge($media, $book_details ?: []);
                break;
            case 'film':
                $film_query = "
                SELECT f.*, c.name AS classification_name 
                FROM films f 
                LEFT JOIN classifications c ON f.classification_id = c.id 
                WHERE f.media_id = ?
                ";
                $film_details = db_select_one($film_query, [$id]);
                $media = array_merge($media, $film_details ?: []);
                break;
            case 'game':
                $game_query = "
                SELECT g.*, p.name AS platform_name 
                FROM games g 
                LEFT JOIN platforms p ON g.platform_id = p.id 
                WHERE g.media_id = ?
                ";
                $game_details = db_select_one($game_query, [$id]);
                $media = array_merge($media, $game_details ?: []);
                break;
        }
    }

    return $media;
}

/**
 * Crée un nouveau média
 */
function create_media($title, $media_type, $genre_id, $stock, $cover, $specific_data)
{
    // Insérer dans la table medias
    $query = "INSERT INTO medias (title, media_type, genre_id, stock, images) VALUES (?, ?, ?, ?, ?)";

    if (db_execute($query, [$title, $media_type, $genre_id, $stock, $cover])) {
        $media_id = db_last_insert_id();

        // Insérer les données spécifiques selon le type
        switch ($media_type) {
            case 'book':
                $book_query = "INSERT INTO books (media_id, author, isbn, pages, summary, year) VALUES (?, ?, ?, ?, ?, ?)";
                return db_execute($book_query, [
                    $media_id,
                    $specific_data['author'],
                    $specific_data['isbn'],
                    $specific_data['pages'],
                    $specific_data['summary'],
                    $specific_data['year']
                ]);
            case 'film':
                $film_query = "INSERT INTO films (media_id, director, year, duration, synopsis, classification_id) VALUES (?, ?, ?, ?, ?, ?)";
                return db_execute($film_query, [
                    $media_id,
                    $specific_data['director'],
                    $specific_data['year'],
                    $specific_data['duration'],
                    $specific_data['synopsis'],
                    $specific_data['classification_id']
                ]);
            case 'game':
                $game_query = "INSERT INTO games (media_id, publisher, platform_id, minimum_age, description, year) VALUES (?, ?, ?, ?, ?, ?)";
                return db_execute($game_query, [
                    $media_id,
                    $specific_data['publisher'],
                    $specific_data['platform_id'],
                    $specific_data['minimum_age'],
                    $specific_data['description'],
                    $specific_data['year']
                ]);
        }
    }

    return false;
}

/**
 * Met à jour un média
 */
function update_media($id, $title, $genre_id, $stock, $specific_data)
{
    // Mettre à jour la table medias
    $query = "UPDATE medias SET title = ?, genre_id = ?, stock = ? WHERE id = ?";

    if (db_execute($query, [$title, $genre_id, $stock, $id])) {
        // Récupérer le type de média
        $media = get_media_by_id($id);

        if ($media) {
            switch ($media['media_type']) {
                case 'book':
                    $book_query = "UPDATE books SET author = ?, isbn = ?, pages = ?, summary = ?, year = ? WHERE media_id = ?";
                    return db_execute($book_query, [
                        $specific_data['author'],
                        $specific_data['isbn'],
                        $specific_data['pages'],
                        $specific_data['summary'],
                        $specific_data['year'],
                        $id
                    ]);
                case 'film':
                    $film_query = "UPDATE films SET director = ?, year = ?, duration = ?, synopsis = ?, classification_id = ? WHERE media_id = ?";
                    return db_execute($film_query, [
                        $specific_data['director'],
                        $specific_data['year'],
                        $specific_data['duration'],
                        $specific_data['synopsis'],
                        $specific_data['classification_id'],
                        $id
                    ]);
                case 'game':
                    $game_query = "UPDATE games SET publisher = ?, platform_id = ?, minimum_age = ?, description = ?, year = ? WHERE media_id = ?";
                    return db_execute($game_query, [
                        $specific_data['publisher'],
                        $specific_data['platform_id'],
                        $specific_data['minimum_age'],
                        $specific_data['description'],
                        $specific_data['year'],
                        $id
                    ]);
            }
        }
    }

    return false;
}

/**
 * Supprime un média
 */
function delete_media($id)
{
    // Vérifier s'il y a des emprunts en cours
    $loan_check = "SELECT COUNT(*) as count FROM loans WHERE media_id = ? AND status = 'En cours'";
    $result = db_select_one($loan_check, [$id]);

    if ($result['count'] > 0) {
        return false; // Ne peut pas supprimer si des emprunts sont en cours
    }

    // Supprimer le média (les tables spécifiques seront supprimées automatiquement grâce à ON DELETE CASCADE)
    $query = "DELETE FROM medias WHERE id = ?";
    return db_execute($query, [$id]);
}

/**
 * Récupère tous les genres
 */
function get_all_genres()
{
    $query = "SELECT * FROM genres ORDER BY name";
    return db_select($query);
}

/**
 * Récupère toutes les plateformes
 */
function get_all_platforms()
{
    $query = "SELECT * FROM platforms ORDER BY name";
    return db_select($query);
}

/**
 * Récupère toutes les classifications
 */
function get_all_classifications()
{
    $query = "SELECT * FROM classifications ORDER BY name";
    return db_select($query);
}

/**
 * Vérifie si un ISBN existe déjà
 */
function isbn_exists($isbn, $exclude_id = null)
{
    $query = "SELECT COUNT(*) as count FROM books WHERE isbn = ?";
    $params = [$isbn];

    if ($exclude_id) {
        $query .= " AND media_id != ?";
        $params[] = $exclude_id;
    }

    $result = db_select_one($query, $params);
    return $result['count'] > 0;
}
