<?php 

function get_all_medias() {
    $query = "SELECT 
                m.id AS media_id,
                m.title,
                m.media_type,
                m.images,
                b.author,
                f.director,
                g.publisher
            FROM medias m
            LEFT JOIN books b ON m.id = b.media_id
            LEFT JOIN films f ON m.id = f.media_id
            LEFT JOIN games g ON m.id = g.media_id
            WHERE 1=1";
    return db_select($query);
}

function search_medias($filters) {
    $sql = "SELECT 
                m.id AS media_id,
                m.title,
                m.media_type,
                b.author,
                m.images,
                f.director,
                g.publisher,
                ge.name
            FROM medias m
            LEFT JOIN books b ON m.id = b.media_id
            LEFT JOIN films f ON m.id = f.media_id
            LEFT JOIN games g ON m.id = g.media_id
            LEFT JOIN genres ge ON m.genre_id = ge.id
            WHERE 1=1";
    
    $params = [];

    if (!empty($filters['titre'])) {
        $sql .= " AND m.title LIKE :titre";
        $params['titre'] = "%" . $filters['titre'] . "%";
    }

    if (!empty($filters['type'])) {
        $sql .= " AND m.media_type = :type";
        $params['type'] = $filters['type'];
    }

    if (!empty($filters['genre'])) {
        $sql .= " AND ge.name = :genre";
        $params['genre'] = $filters['genre'];
    }

    if (!empty($filters['disponibilite'])) {
        if ($filters['disponibilite'] == 'disponible') {
            $sql .= " AND m.id NOT IN (SELECT media_id FROM loans WHERE return_date IS NULL)";
        } elseif ($filters['disponibilite'] == 'emprunte') {
            $sql .= " AND m.id IN (SELECT media_id FROM loans WHERE return_date IS NULL)";
        }
    }

    $sql .= " ORDER BY m.id DESC";

    return db_select($sql, $params);
}



function media_detail($id) {

$query = "SELECT 
                
                m.id AS media_id,
                m.title,
                m.media_type,
                m.images,
                b.author,
                f.director,
                g.publisher
            FROM medias m
            LEFT JOIN books b ON m.id = b.media_id
            LEFT JOIN films f ON m.id = f.media_id
            LEFT JOIN games g ON m.id = g.media_id
            WHERE m.id = ?";


$params = [$id];

    return db_select_one($query, $params);

}



function count_medias() {
    $sql = "SELECT COUNT(*) AS nb_medias FROM medias";
    $result = db_select_one($sql);
    return (int) $result['nb_medias'];
}

function get_medias_paginated($limit, $offset) {
    $sql = "SELECT 
                m.id AS media_id,
                m.title,
                m.media_type,
                m.images,
                b.author,
                f.director,
                g.publisher
            FROM medias m
            LEFT JOIN books b ON m.id = b.media_id
            LEFT JOIN films f ON m.id = f.media_id
            LEFT JOIN games g ON m.id = g.media_id
            ORDER BY m.created_at DESC
            LIMIT :limit OFFSET :offset";

    $params = [
        'limit' => (int)$limit,
        'offset' => (int)$offset
    ];

    return db_select($sql, $params);
}

function paginate_medias($currentPage = 1, $perPage = 20) {
    // Nombre total de médias
    $nbMedias = count_medias();

    // Nombre de pages total
    $pages = ceil($nbMedias / $perPage);

    // Calcul du premier enregistrement
    $offset = ($currentPage - 1) * $perPage;

    // Récupération des médias paginés
    $medias = get_medias_paginated($perPage, $offset);

    return [
        'medias' => $medias,
        'pages' => $pages,
        'currentPage' => $currentPage
    ];
}



