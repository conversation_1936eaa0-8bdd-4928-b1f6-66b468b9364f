/* Variables globales */
:root {
  --primary: #4a90e2;
  --primary-hover: #357ABD;
  --secondary: #7b8fa1;
  --accent: #f5a623;
  --background: #f9f9f9;
  --white: #ffffff;
  --dark: #2c3e50;
  --light-grey: #e1e8ed;
  --error: #e74c3c;
  --success: #2ecc71;
  --marine: #0b1d3a; 
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --radius: 8px;
  --font: 'Segoe UI', sans-serif;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font);
  background-color: var(--background);
  color: var(--dark);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Containers */
.container {
  max-width: 1200px;
  margin: auto;
  padding: 0 1rem;
}


/* Header */
.header {
background: #cca144;
box-shadow: var(--shadow);
position: sticky;
top: 0;
z-index: 10;
padding: 0 2rem;
}


.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.nav-brand a {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--white);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-menu a {
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #5a5f0d;
}



/* Hero */
.hero {
  background: linear-gradient(135deg, #fdffe3, #ffffff);
  text-align: center;
  padding: 4rem 1rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--dark); 
}


.hero p {
  font-size: 1.2rem;
  color: var(--secondary);
  margin-bottom: 2rem;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}


/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: background 0.3s ease;
}

.btn-primary {
  background: #c97f24;
  color: var(--white);
}

.btn-primary:hover {
  background: #5a5f0d;
}

.btn-secondary {
  background: #d59a51;
  color: var(--white);
}

.btn-secondary:hover {
   background: #5a5f0d;
}

/* Features */
.features {
  padding: 4rem 1rem;
  background: var(--white); 
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 900px; 
  margin: 0 auto; 
}

.feature-card {
  background: #b68315;
  padding: 2rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  text-align: center;
  transition: transform 0.3s ease;
  min-height:150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}


.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-card h3 {
  margin-bottom: 1rem;
}


.feature-card h3 a {
  color: var(--white);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: bold;
  display: inline-block;
  transition: color 0.3s ease;
}

.feature-card h3 a:hover {
  color: #5a5f0d;
}

.getting-started h2 {
  text-align: center;
}

.getting-started {
  text-align: center;
}



/* Formulaires */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--light-grey);
  border-radius: var(--radius);
  background: var(--white);
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}


/* Steps */

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.step {
  background:#cb934a; 
  border: 2px solid var(--light-grey); 
  border-radius: var(--radius);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow);
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 4rem;
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.step-number {
  width: 40px;
  height: 40px;
  background: #5a5f0d;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 1rem;
  font-size: 1.2rem;
}

.step h3 {
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: white;
}

.step p {
  color: black;
  font-size: 1rem;
}

/* Authentification */
.auth-card {
  background-color: var(--dark);
  padding: 2.5rem 3rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  max-width: 420px;
  width: 100%;
  text-align: start;
  color: var(--white);

  max-height: 90vh;  
  overflow-y: auto;  
}
.auth-container {
  min-height: 80vh; 
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 1rem;
  background-color: var(--background);
  flex-direction: column;
}

.auth-header h1 {
  color: var(--white);
  margin-bottom: 0.3rem;
}

.auth-header p {
  color: var(--secondary);
  margin-bottom: 2rem;
  font-size: 1rem;
}


.auth-form label {
  color: var(--white);
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}


.auth-form input {
  background-color: var(--white);
  color: var(--dark);
  border: 1px solid var(--light-grey);
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: var(--radius);
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.auth-form input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

/* Boutons */
.btn-full {
  width: 100%;
  padding: 0.85rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 1rem;
}

.auth-footer {
  margin-top: 1.5rem;
  color: var(--secondary);
  font-size: 0.9rem;
}

.auth-footer a {
  color: var(--accent); 
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-footer a:hover {
  color: var(--white);
  text-decoration: underline;
}



/* Alerts */
.alert {
  padding: 1rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  font-weight: 500;
}

.alert-success {
  background: #eafaf1;
  color: var(--success);
}

.alert-error {
  background: #fdecea;
  color: var(--error);
}


/* Contact */
.sidebar {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 2rem;
  margin-bottom: 4rem; 
}

.sidebar .info-box {
  flex: 1;
  min-width: 260px;
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}


/* Footer */
.footer {
  background: #cca144;
  color: var(--white);
  text-align: center;
  padding: 2rem 1rem;
  margin-top: auto;
}


.footer p {
  margin: 0.5rem 0;
}


.content-main h2 {
  margin-bottom: 2rem;
}

.content-main h3 {
  margin-bottom: 1.5rem;
}

.content-main p,
.content-main ul {
  margin-bottom: 1.2rem;
}




/* Responsive */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    flex-direction: row;
    gap: 1rem;
    align-items: center;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .features h2 {
    font-size: 2rem;
  }
  .sidebar {
    flex-direction: column;
  }
}


@media (max-width: 480px) {
  .login-form, .register-form {
    padding: 1.5rem 1rem;
    max-width: 90%;
  }
}
