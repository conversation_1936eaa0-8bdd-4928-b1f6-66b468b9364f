:root {
  --primary-color: #000000;
  --primary-hover: #413315;
  --secondary-color: #64748b;
  --success-color: #059669;
  --outline-color: #153d15;
  --error-color: #dc2626;
  --warning-color: #d97706;
  --background-color: #b68215;
  --text-color: #111827;
  --border-color: #d1d5db;
  --white: #ffffff;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

a {
  text-decoration: none;
  color: var(--text-color);
}

li {
  list-style-type: none;
}

.back-link {
  color: var(--background-color);
  font-size: large;
}

.required {
  color: var(--error-color);
}
.container h1 {
  text-align: center;
  margin: 2rem 0;
  font-size: 2.3rem;
}

.content-flex {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
}

.content-flex .content-nav ul {
  display: flex;
  gap: 1rem;
  justify-content: space-around;
  align-items: center;
  flex-direction: column;
  min-width: 60vw;
  margin-bottom: 2rem;
}

.content-flex .content-nav ul li {
  background-color: var(--background-color);
  padding: 0.8rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  width: 100%;
  text-align: center;
  color: var(--white);
}

.content-main {
  width: 100%;
  overflow-x: auto;
}

.user-table {
  border-collapse: collapse;
  width: 100%;
}

.user-table th,
.user-table td {
  border: 1px transparent;
  padding: 0.5rem;
  text-align: left;
}

.user-table th {
  background-color: #f9fafb;
}

/* Page Detail d'utilisateur */
.user-details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.user-info-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.user-info-card h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.user-info-card p {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.6;
}

.user-info-card strong {
  color: var(--text-color);
  font-weight: 600;
  min-width: 120px;
  display: inline-block;
}

.stats-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.stats-card h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: var(--secondary-color);
  font-weight: 500;
}

.loans-section {
  background: var(--white);
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.loans-section h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.loans-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background: var(--white);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.loans-table thead {
  background: var(--background-color);
  color: var(--white);
}

.loans-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loans-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

.loans-table tbody tr:hover {
  background: #f9fafb;
  transition: background-color 0.3s ease;
}

.loans-table tbody tr:last-child td {
  border-bottom: none;
}

/* Status badges */
.status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.cours {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.status.retard {
  background: rgba(220, 38, 38, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.status.retourné {
  background: rgba(100, 116, 139, 0.1);
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
}

/* Un état vide */
.loans-section p {
  text-align: center;
  color: var(--secondary-color);
  font-style: italic;
  padding: 2rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  border: 2px dashed var(--border-color);
}

/* Un design responsive */
@media (min-width: 600px) {
  .content-flex .content-nav ul li {
    width: 60%;
  }
}

@media (min-width: 768px) {
  .user-details-grid {
    grid-template-columns: 2fr 1fr;
  }
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .content-flex .content-nav ul {
    flex-direction: row;
    justify-content: space-between;
  }
  .content-flex .content-nav ul li {
    width: 50%;
  }
  .content-flex .content-main {
    width: auto;
    overflow-x: unset;
  }
}

@media (min-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  .content-flex .content-nav ul li {
    width: 13rem;
  }
  .content-flex .content-main {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .loans-table {
    font-size: 0.8rem;
  }
  .loans-table th,
  .loans-table td {
    padding: 0.5rem;
  }
  .stat-number {
    font-size: 1.5rem;
  }
  .user-info-card,
  .stats-card,
  .loans-section {
    padding: 1.5rem;
  }
}

/* ============ MEDIA MANAGEMENT STYLES ============ */

/* Actions bar */
.actions-bar {
  margin-bottom: 2rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.btn-primary {
  background: var(--background-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--white);
}

.btn-secondary:hover {
  background: #475569;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--background-color);
}

.btn-outline:hover {
  background: var(--background-color);
  color: var(--white);
}

.btn-danger {
  background: var(--error-color);
  color: var(--white);
}

.btn-danger:hover {
  background: #b91c1c;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Filters section */
.filters-section {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.filters-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: end;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Stats summary */
.stats-summary {
  margin-bottom: 1rem;
  font-weight: 500;
  color: var(--secondary-color);
}

/* Medias section */
.medias-section {
  background: var(--white);
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.table-responsive {
  overflow-x: auto;
}

.medias-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
}

.medias-table thead {
  background: var(--background-color);
  color: var(--white);
}

.medias-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.medias-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

.medias-table tbody tr:hover {
  background: #f9fafb;
  transition: background-color 0.3s ease;
}

.medias-table tbody tr:last-child td {
  border-bottom: none;
}

/* Media type badges */
.media-type {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.media-type.book {
  background: rgba(5, 150, 105, 0.1);
  color: #047857;
  border: 1px solid #047857;
}

.media-type.film {
  background: rgba(124, 58, 237, 0.1);
  color: #6d28d9;
  border: 1px solid #6d28d9;
}

.media-type.game {
  background: rgba(217, 119, 6, 0.1);
  color: #b45309;
  border: 1px solid #b45309;
}

/* Stock badges */
.stock-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.stock-badge.available {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.stock-badge.unavailable {
  background: rgba(220, 38, 38, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* Actions column */
.actions {
  white-space: nowrap;
}

.actions .btn {
  margin: 0 0.25rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f9fafb;
  border-top: 1px solid var(--border-color);
}

.page-info {
  font-weight: 500;
  color: var(--secondary-color);
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--secondary-color);
}

.empty-state p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

/* ============ FORM STYLES ============ */

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.media-form {
  background: var(--white);
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.form-section h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group label::after {
  content: " *";
  color: var(--error-color);
}

.form-group label:not([for$="*"])::after {
  content: "";
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control[readonly] {
  background: #f9fafb;
  color: var(--secondary-color);
}

.form-text {
  font-size: 0.875rem;
  color: var(--secondary-color);
  margin-top: 0.25rem;
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.specific-fields {
  display: none;
}

/* ============ CONFIRMATION STYLES ============ */

.confirmation-container {
  max-width: 600px;
  margin: 0 auto;
}

.warning-box {
  background: var(--white);
  border: 2px solid var(--warning-color);
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.warning-icon {
  font-size: 3rem;
  color: var(--warning-color);
  margin-bottom: 1rem;
}

.warning-box h2 {
  color: var(--error-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.media-info {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  text-align: left;
}

.media-info h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.media-details p {
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.warning-message {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid var(--error-color);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  color: var(--error-color);
}

.warning-message p {
  margin-bottom: 0.5rem;
}

.warning-message p:last-child {
  margin-bottom: 0;
}

.confirmation-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* ============ RESPONSIVE DESIGN ============ */

@media (max-width: 768px) {
  .filters-form {
    flex-direction: column;
  }

  .filter-group {
    min-width: auto;
  }

  .medias-table {
    font-size: 0.8rem;
  }

  .medias-table th,
  .medias-table td {
    padding: 0.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .confirmation-actions {
    flex-direction: column;
  }

  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
}


