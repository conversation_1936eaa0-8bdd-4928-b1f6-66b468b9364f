
:root {
  --background-2: #eac257;
  --primary: #4a90e2;
  --primary-hover: #357ABD;
  --secondary: #7b8fa1;
  --accent: #f5a623;
  --background: #f9f9f9;
  --white: #ffffff;
  --dark: #2c3e50;
  --light-grey: #e1e8ed;
  --error: #e74c3c;
  --success: #2ecc71;
  --marine: #0b1d3a; 
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --radius: 8px;
  --font: 'Segoe UI', sans-serif;
}


.main-content-wrapper {
  position: relative;
  min-height: 100vh; 
  overflow: hidden;
}

.background-blur {
position: absolute;
top: 0; left: 0; right: 0; bottom: 0;
background-image: url('../images/bibli.jpg');
background-size: 1000px 700px;
background-position: center;
background-repeat: repeat;
filter: blur(2px);
opacity: 0.;  
z-index: -1;

}

.main-content {
  position: relative;
  z-index: 1;
  padding: 20px; 

}



.search-section {
  padding: 3rem 1rem;
  background-color: rgb(197, 128, 9);
}

.search-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.search-bar {
  display: flex;
  justify-content: center; 
  width: 100%;
  padding-bottom: 1rem;
  gap: 0.5;

}

.search-bar input {
  flex: 1;
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--light-grey);
  border-radius: var(--radius);
  font-size: 1rem;
  background-color: var(--white);
  color: var(--dark);
  box-sizing: border-box;
  max-width: 600px;
}

.search-bar button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: var(--radius);
  background-color: var(--primary);
  color: var(--white);
  border: none;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-left: 1rem;
}

.search-bar button:hover {
  background-color: var(--primary-hover);
}

.search-form {
  background-color: black; 
  padding: 2rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px; 
  margin: 0 auto; 
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  
}

.filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  width: 100%;
}

.filters select,
.filters button {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--light-grey);
  background-color: var(--white);
  color: var(--dark);
  min-width: 150px;
  cursor: pointer;
  
}

.filters button {
  background-color: var(--primary);
  color: var(--white);
  border: none;
  transition: background 0.3s ease;
}

.filters button:hover {
  background-color: var(--primary-hover);
}


.catalog-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;
  list-style: none;
}

.card {
  background: #cca144;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.media-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 10px;
}

.card-title {
  font-size: 1.1em;
  margin: 10px 0 5px;
  text-align: center;
  color: black;

}

.card-info {
  font-size: 0.9em;
  color: var(--white);
  display: block;
  text-align: center;
  font-weight: bold;
  
}

.card a {
  text-decoration: none;
}

.media-detail-container {
  max-width: 700px;
  margin: 40px auto;
  padding: 20px;
  background: var(--background-2);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  text-align: center;         
  display: flex;            
  flex-direction: column;     
  align-items: center;  
}


.media-detail-container h1 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #222;
}


.media-detail-container img {
  display: block;
  align-content: center;
  max-width: 100%;
  height: auto;
  margin-bottom: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}


.media-detail-container p {
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.media-detail-container strong {
  color: #555;
}


.media-detail-container a {
  display: inline-block;
  margin-top: 30px;
  text-decoration: none;
  color: #0066cc;
  font-weight: 600;
  transition: color 0.3s ease;
}

.media-detail-container a:hover {
  color: #004999;
  text-decoration: underline;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 30px 0;
  flex-wrap: wrap;
}

.pagination a {
  display: inline-block;
  padding: 8px 12px;
  background: #cca144; /* couleur proche de tes cartes */
  color: #fff;
  border-radius: var(--radius);
  text-decoration: none;
  font-weight: bold;
  transition: background 0.3s ease;
}

.pagination a:hover {
  background: var(--primary-hover);
}

.pagination a.active {
  background: var(--primary);
}

.pagination a.prev,
.pagination a.next {
  font-weight: normal;
}
