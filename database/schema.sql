CREATE DATABASE IF NOT EXISTS php_mvc_app CHARACTER SET utf8 COLLATE utf8_general_ci;

USE php_mvc_app;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE genres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE
);

CREATE TABLE platforms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE
);

CREATE TABLE classifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

CREATE TABLE medias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    media_type ENUM('book', 'film', 'game') NOT NULL,
    genre_id INT,
    images VARCHAR(50),
    stock INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (genre_id) REFERENCES genres (id)
);

CREATE TABLE books (
    media_id INT PRIMARY KEY,
    author VARCHAR(255) NOT NULL,
    isbn VARCHAR(13) NOT NULL UNIQUE,
    pages INT,
    summary TEXT,
    year INT NOT NULL,
    FOREIGN KEY (media_id) REFERENCES medias (id) ON DELETE CASCADE
);

CREATE TABLE films (
    media_id INT PRIMARY KEY,
    director VARCHAR(255) NOT NULL,
    year INT NOT NULL,
    duration INT,
    synopsis TEXT,
    classification_id INT,
    FOREIGN KEY (media_id) REFERENCES medias (id) ON DELETE CASCADE,
    FOREIGN KEY (classification_id) REFERENCES classifications (id)
);

CREATE TABLE games (
    media_id INT PRIMARY KEY,
    publisher VARCHAR(255) NOT NULL,
    platform_id INT,
    minimum_age INT,
    description TEXT,
    year INT NOT NULL,
    FOREIGN KEY (media_id) REFERENCES medias (id) ON DELETE CASCADE,
    FOREIGN KEY (platform_id) REFERENCES platforms (id)
);

CREATE TABLE loans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    media_id INT NOT NULL,
    loan_date DATE NOT NULL,
    due_date DATE NOT NULL,
    return_date DATE NULL,
    status ENUM(
        'En cours',
        'Retourné',
        'En retard'
    ) DEFAULT 'En cours',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
    FOREIGN KEY (media_id) REFERENCES medias (id)
);

CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM(
        'debug',
        'info',
        'warning',
        'error'
    ) DEFAULT 'info',
    message TEXT NOT NULL,
    context JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL
);

CREATE TABLE sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT,
    data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE INDEX idx_loans_user_status ON loans (user_id, status);

CREATE INDEX idx_loans_media_status ON loans (media_id, status);

CREATE INDEX idx_media_type ON medias (media_type);

CREATE INDEX idx_media_stock ON medias (stock);

CREATE INDEX idx_media_genre ON medias (genre_id);

CREATE INDEX idx_loans_dates ON loans (loan_date, due_date);

-- === 1. Insertion dans la table users ===
INSERT INTO
    users (
        first_name,
        last_name,
        email,
        password,
        role
    )
VALUES (
        'Jean',
        'Dupont',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Marie',
        'Martin',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Pierre',
        'Durand',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Sophie',
        'Bernard',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Thomas',
        'Petit',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Camille',
        'Robert',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Lucas',
        'Moreau',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Emma',
        'Simon',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Alex',
        'Laurent',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Julie',
        'Michel',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    );

INSERT INTO
    users (
        first_name,
        last_name,
        email,
        password,
        role
    )
VALUES (
        'Antoine',
        'Leroy',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    ('John Doe', '<EMAIL>', '$2y$10$/vD8hGtkBJsAae2TiSkbV.jg0bnNDAFv8xBewH14.OKvR0PpeVbq6', 'user'),
('Jane Smith', '<EMAIL>', '$2y$10$/vD8hGtkBJsAae2TiSkbV.jg0bnNDAFv8xBewH14.OKvR0PpeVbq6', 'user'),
('Admin User', '<EMAIL>', '$2y$10$/vD8hGtkBJsAae2TiSkbV.jg0bnNDAFv8xBewH14.OKvR0PpeVbq6', 'user'),
('Antoine', 'Leroy', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Clara', 'Dubois', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Maxime', 'Roux', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Léa', 'Fournier', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Hugo', 'Girard', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Chloé', 'Bonnet', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Nathan', 'Dupuis', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Manon', 'Lambert', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Théo', 'Faure', '<EMAIL>', '$2y$10$examplehash', 'user'),
('Inès', 'Mercier', '<EMAIL>', '$2y$10$examplehash', 'user')
    (
        'Clara',
        'Dubois',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Maxime',
        'Roux',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Léa',
        'Fournier',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Hugo',
        'Girard',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Chloé',
        'Bonnet',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Nathan',
        'Dupuis',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Manon',
        'Lambert',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Théo',
        'Faure',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    ),
    (
        'Inès',
        'Mercier',
        '<EMAIL>',
        '$2y$10$examplehash',
        'user'
    );

-- === 2. Insertion dans la table genres ===
INSERT INTO
    genres (name)
VALUES ('Science-Fiction'),
    ('Fantastique'),
    ('Policier'),
    ('Histoire'),
    ('Aventure'),
    ('Horreur'),
    ('Romance'),
    ('Documentaire'),
    ('Gamme');

-- === 3. Insertion dans la table platforms ===
INSERT INTO
    platforms (name)
VALUES ('PlayStation 5'),
    ('Xbox Series X'),
    ('Nintendo Switch'),
    ('PC'),
    ('PlayStation 4');

-- === 4. Insertion dans la table classifications ===
INSERT INTO
    classifications (name, description)
VALUES ('TP', 'Tout public'),
    (
        '-12',
        'Interdit aux moins de 12 ans'
    ),
    (
        '-16',
        'Interdit aux moins de 16 ans'
    ),
    (
        '-18',
        'Interdit aux moins de 18 ans'
    );

-- === 5. Insertion dans la table medias (initiale + 15 nouveaux) ===
INSERT INTO
    medias (
        title,
        media_type,
        genre_id,
        stock,
        images
    )
VALUES (
        'Dune',
        'book',
        1,
        5,
        'dune.jpg'
    ),
    (
        'Le Seigneur des Anneaux',
        'book',
        2,
        3,
        'seigneur_des_anneaux.jpg'
    ),
    (
        'Inception',
        'film',
        1,
        7,
        'inception.jpg'
    ),
    (
        'The Last of Us',
        'game',
        5,
        4,
        'the_last_of_us.jpeg'
    ),
    (
        'Titanic',
        'film',
        7,
        6,
        'titanic.jpg'
    ),
    (
        'Neuromancien',
        'book',
        1,
        4,
        'neuromancien.jpg'
    ),
    (
        'Harry Potter à l\'école des sorciers',
        'book',
        2,
        8,
        'harry_potter.jpg'
    ),
    (
        'Les Déracinés',
        'book',
        4,
        3,
        'les_deracines.jpg'
    ),
    (
        'Fondation',
        'book',
        1,
        6,
        'fondation.jpg'
    ),
    (
        'L\'Assassin Royal - Le Pari du fou',
        'book',
        2,
        5,
        'assassin_royal.jpg'
    ),
    (
        'Interstellar',
        'film',
        1,
        9,
        'interstellar.jpg'
    ),
    (
        'Parasite',
        'film',
        3,
        7,
        'parasite.jpeg'
    ),
    (
        'Mad Max: Fury Road',
        'film',
        5,
        6,
        'mad_max.jpg'
    ),
    (
        'Get Out',
        'film',
        6,
        5,
        'get_out.jpeg'
    ),
    (
        'La Haine',
        'film',
        3,
        4,
        'la_haine.jpg'
    ),
    (
        'Elden Ring',
        'game',
        2,
        3,
        'elden_ring.jpg'
    ),
    (
        'God of War Ragnarök',
        'game',
        5,
        4,
        'god_of_war.jpeg'
    ),
    (
        'Disco Elysium',
        'game',
        3,
        5,
        'disco_elysium.jpeg'
    ),
    (
        'Cyberpunk 2077',
        'game',
        1,
        6,
        'cyberpunk2077.jpeg'
    ),
    (
        'Hollow Knight',
        'game',
        5,
        7,
        'hollow_knight.jpeg'
    );

INSERT INTO
    medias (
        title,
        media_type,
        genre_id,
        stock,
        images
    )
VALUES
    -- Livres (21-40)
    (
        '1984',
        'book',
        1,
        4,
        '1984.jpeg'
    ),
    (
        'Le Petit Prince',
        'book',
        2,
        6,
        'petit_prince.jpg'
    ),
    (
        'Orgueil et Préjugés',
        'book',
        7,
        3,
        'orgueil_prejuges.jpg'
    ),
    (
        'Fahrenheit 451',
        'book',
        1,
        5,
        'fahrenheit.jpg'
    ),
    (
        'Les Misérables',
        'book',
        4,
        2,
        'les_miserables.jpg'
    ),
    (
        'Dracula',
        'book',
        6,
        4,
        'dracula.jpg'
    ),
    (
        'Sherlock Holmes',
        'book',
        3,
        7,
        'sherlock_holmes.jpg'
    ),
    (
        'Le Hobbit',
        'book',
        2,
        5,
        'the_hobbit.jpeg'
    ),
    (
        'Gatsby le Magnifique',
        'book',
        7,
        3,
        'gatsby_lemagnifique.jpeg'
    ),
    (
        'L\'Étranger',
        'book',
        4,
        4,
        'letranger.jpeg'
    ),
    (
        'Brave New World',
        'book',
        1,
        6,
        'brave_new_world.jpg'
    ),
    (
        'Jane Eyre',
        'book',
        7,
        3,
        'jane_eyre.jpg'
    ),
    (
        'Les Fleurs du Mal',
        'book',
        4,
        2,
        'Les_Fleurs_du_mal.jpg'
    ),
    (
        'Frankenstein',
        'book',
        6,
        5,
        'frankenstein.jpg'
    ),
    (
        'Le Comte de Monte-Cristo',
        'book',
        5,
        4,
        'compte_monte_cristo.jpg'
    ),
    (
        'Wuthering Heights',
        'book',
        7,
        3,
        'wuthering_heights.jpg'
    ),
    (
        'L\'Odyssée',
        'book',
        5,
        6,
        'odysee.jpg'
    ),
    (
        'Crime et Châtiment',
        'book',
        3,
        4,
        'crime_chatiment.jpeg'
    ),
    (
        'Anna Karénine',
        'book',
        7,
        3,
        'anna_karenine.jpg'
    ),
    (
        'Don Quichotte',
        'book',
        5,
        2,
        'don_quichotte.jpg'
    ),
    -- Films (41-60)
    (
        'Pulp Fiction',
        'film',
        3,
        8,
        'pulp_fiction.jpg'
    ),
    (
        'Le Parrain',
        'film',
        3,
        6,
        'le_parrain.jpg'
    ),
    (
        'Casablanca',
        'film',
        7,
        5,
        'casablanca.jpg'
    ),
    (
        'Citizen Kane',
        'film',
        4,
        4,
        'citizen_kane.jpeg'
    ),
    (
        'Vertigo',
        'film',
        3,
        7,
        'vertigo.jpeg'
    ),
    (
        'Psycho',
        'film',
        6,
        6,
        'psycho_film.jpg'
    ),
    (
        'Apocalypse Now',
        'film',
        4,
        5,
        'apocalypse_now.jpg'
    ),
    (
        '2001: L\'Odyssée de l\'espace',
        'film',
        1,
        8,
        'odysee.jpg'
    ),
    (
        'Taxi Driver',
        'film',
        3,
        6,
        'taxi_driver.jpg'
    ),
    (
        'Goodfellas',
        'film',
        3,
        7,
        'goodfellas.jpg'
    ),
    (
        'The Shining',
        'film',
        6,
        5,
        'the_shining.jpg'
    ),
    (
        'Alien',
        'film',
        1,
        8,
        'alien.jpeg'
    ),
    (
        'Blade Runner',
        'film',
        1,
        6,
        'blade_runner.jpg'
    ),
    (
        'Terminator 2',
        'film',
        1,
        7,
        'terminator.jpg'
    ),
    (
        'Matrix',
        'film',
        1,
        9,
        'Matrix-film.jpg'
    ),
    (
        'Fight Club',
        'film',
        3,
        8,
        'fight_club_film.jpg'
    ),
    (
        'Seven',
        'film',
        3,
        6,
        'Seven_film.jpg'
    ),
    (
        'Silence of the Lambs',
        'film',
        6,
        7,
        'The_Silence_of_the_lambs.jpg'
    ),
    (
        'Forrest Gump',
        'film',
        7,
        5,
        'forrest_gump.jpg'
    ),
    (
        'Shrek',
        'film',
        2,
        8,
        'shrek.jpg'
    ),
    -- Jeux vidéo (61-80)
    (
        'The Witcher 3',
        'game',
        2,
        5,
        'the_witcher.jpg'
    ),
    (
        'Red Dead Redemption 2',
        'game',
        5,
        4,
        'red_dead_redemption.jpeg'
    ),
    (
        'Grand Theft Auto V',
        'game',
        5,
        6,
        'grand_theft_auto.jpeg'
    ),
    (
        'Minecraft',
        'game',
        5,
        8,
        'minecraft_game.png'
    ),
    (
        'The Legend of Zelda: Breath of the Wild',
        'game',
        2,
        7,
        'The_Legend_of_Zelda.jpg'
    ),
    (
        'Super Mario Odyssey',
        'game',
        5,
        6,
        'super_mario.jpg'
    ),
    (
        'Dark Souls III',
        'game',
        2,
        4,
        'dark_soul.jpg'
    ),
    (
        'Overwatch',
        'game',
        5,
        5,
        'overwatch_game.jpg'
    ),
    (
        'Call of Duty: Modern Warfare',
        'game',
        5,
        7,
        'call_of_duty_warfare.jpg'
    ),
    (
        'FIFA 23',
        'game',
        5,
        8,
        'fifa_game_2023.jpeg'
    ),
    (
        'Assassin\'s Creed Valhalla',
        'game',
        4,
        5,
        'assassin_royal.jpg'
    ),
    (
        'Spider-Man',
        'game',
        5,
        6,
        'spider_man.jpg'
    ),
    (
        'Horizon Zero Dawn',
        'game',
        1,
        4,
        'horizon_zero_dawn.jpeg'
    ),
    (
        'Uncharted 4',
        'game',
        5,
        5,
        'uncharted_4.jpg'
    ),
    (
        'Bloodborne',
        'game',
        6,
        3,
        'bloodborne.jpeg'
    ),
    (
        'Persona 5',
        'game',
        2,
        4,
        'persona_5.jpg'
    ),
    (
        'Ghost of Tsushima',
        'game',
        4,
        5,
        'Ghost_of_Tsushima.jpg'
    ),
    (
        'Animal Crossing',
        'game',
        5,
        7,
        'animal_crossing.jpg'
    ),
    (
        'Among Us',
        'game',
        3,
        6,
        'among_us_game.jpg'
    ),
    (
        'Fall Guys',
        'game',
        5,
        8,
        'fall_guys.jpeg'
    );

-- === 6. Insertion dans la table books ===
INSERT INTO
    books (
        media_id,
        author,
        isbn,
        pages,
        summary,
        year
    )
VALUES (
        1,
        'Frank Herbert',
        '9782266123456',
        896,
        'Un jeune noble doit survivre sur une planète désertique.',
        1965
    ),
    (
        2,
        'J.R.R. Tolkien',
        '9782266123457',
        1216,
        'Un hobbit part en quête pour détruire un anneau magique.',
        1954
    ),
    (
        6,
        'William Gibson',
        '9780441569595',
        271,
        'Un hackeur recruté pour une mission dans le cyberespace.',
        1984
    ),
    (
        7,
        'J.K. Rowling',
        '9782070117778',
        309,
        'Un jeune garçon découvre qu''il est un sorcier.',
        1997
    ),
    (
        8,
        'Maurice Barrès',
        '9782070362112',
        512,
        'Un roman sur l''identité nationale et les racines françaises.',
        1897
    ),
    (
        9,
        'Isaac Asimov',
        '9782266111750',
        288,
        'Le début de l''empire galactique en déclin.',
        1951
    ),
    (
        10,
        'Robin Hobb',
        '9782266195385',
        704,
        'Un assassin en quête de rédemption dans un royaume en crise.',
        1995
    );

INSERT INTO
    books (
        media_id,
        author,
        isbn,
        pages,
        summary,
        year
    )
VALUES (
        21,
        'George Orwell',
        '9780451524935',
        328,
        'Une dystopie totalitaire où Big Brother surveille tout.',
        1949
    ),
    (
        22,
        'Antoine de Saint-Exupéry',
        '9782070408504',
        96,
        'Un aviateur rencontre un petit prince venu d\'une autre planète.',
        1943
    ),
    (
        23,
        'Jane Austen',
        '9780141439518',
        432,
        'Elizabeth Bennet et Mr. Darcy dans l\'Angleterre du 19e siècle.',
        1813
    ),
    (
        24,
        'Ray Bradbury',
        '9781451673319',
        249,
        'Dans un futur où les livres sont interdits et brûlés.',
        1953
    ),
    (
        25,
        'Victor Hugo',
        '9782070409228',
        1488,
        'Jean Valjean et sa quête de rédemption dans la France du 19e siècle.',
        1862
    ),
    (
        26,
        'Bram Stoker',
        '9780486411095',
        418,
        'Le comte Dracula terrorise l\'Angleterre victorienne.',
        1897
    ),
    (
        27,
        'Arthur Conan Doyle',
        '9780486474915',
        307,
        'Les enquêtes du célèbre détective de Baker Street.',
        1887
    ),
    (
        28,
        'J.R.R. Tolkien',
        '9780547928227',
        366,
        'Bilbon Sacquet part à l\'aventure avec des nains.',
        1937
    ),
    (
        29,
        'F. Scott Fitzgerald',
        '9780743273565',
        180,
        'Jay Gatsby et son amour obsessionnel pour Daisy.',
        1925
    ),
    (
        30,
        'Albert Camus',
        '9782070360024',
        186,
        'Meursault face à l\'absurdité de l\'existence.',
        1942
    ),
    (
        31,
        'Aldous Huxley',
        '9780060850524',
        311,
        'Une société future contrôlée par la technologie et les drogues.',
        1932
    ),
    (
        32,
        'Charlotte Brontë',
        '9780141441146',
        507,
        'Jane Eyre, orpheline, devient gouvernante et trouve l\'amour.',
        1847
    ),
    (
        33,
        'Charles Baudelaire',
        '9782070322145',
        352,
        'Recueil de poèmes sur la beauté, le mal et la modernité.',
        1857
    ),
    (
        34,
        'Mary Shelley',
        '9780486282114',
        166,
        'Victor Frankenstein crée un monstre qui se retourne contre lui.',
        1818
    ),
    (
        35,
        'Alexandre Dumas',
        '9782070413348',
        1276,
        'Edmond Dantès se venge de ceux qui l\'ont trahi.',
        1844
    ),
    (
        36,
        'Emily Brontë',
        '9780141439556',
        416,
        'Passion destructrice entre Heathcliff et Catherine.',
        1847
    ),
    (
        37,
        'Homère',
        '9782070411319',
        576,
        'Le voyage d\'Ulysse pour rentrer chez lui après la guerre de Troie.',
        -800
    ),
    (
        38,
        'Fiodor Dostoïevski',
        '9782070403943',
        671,
        'Raskolnikov commet un meurtre et affronte sa culpabilité.',
        1866
    ),
    (
        39,
        'Léon Tolstoï',
        '9782070413799',
        1024,
        'Anna Karénine et son amour adultère dans la Russie tsariste.',
        1877
    ),
    (
        40,
        'Miguel de Cervantes',
        '9782070409815',
        1056,
        'Les aventures du chevalier errant Don Quichotte.',
        1605
    );

-- === 7. Insertion dans la table films ===
INSERT INTO
    films (
        media_id,
        director,
        year,
        duration,
        synopsis,
        classification_id
    )
VALUES (
        3,
        'Christopher Nolan',
        2010,
        148,
        'Un voleur d''idées explore les rêves des autres.',
        2
    ),
    (
        5,
        'James Cameron',
        1997,
        194,
        'Une romance tragique à bord du Titanic.',
        1
    ),
    (
        11,
        'Christopher Nolan',
        2014,
        169,
        'Un voyage à travers un trou de ver pour sauver l''humanité.',
        2
    ),
    (
        12,
        'Bong Joon-ho',
        2019,
        132,
        'Une famille pauvre s''infiltre chez une famille riche.',
        3
    ),
    (
        13,
        'George Miller',
        2015,
        120,
        'Une course effrénée dans un monde post-apocalyptique.',
        3
    ),
    (
        14,
        'Jordan Peele',
        2017,
        104,
        'Un homme découvre une sinistre conspiration raciale.',
        3
    ),
    (
        15,
        'Mathieu Kassovitz',
        1995,
        98,
        'Une nuit dans les banlieues parisiennes après un drame.',
        3
    );

INSERT INTO
    films (
        media_id,
        director,
        year,
        duration,
        synopsis,
        classification_id
    )
VALUES (
        41,
        'Quentin Tarantino',
        1994,
        154,
        'Histoires entremêlées de criminels à Los Angeles.',
        3
    ),
    (
        42,
        'Francis Ford Coppola',
        1972,
        175,
        'L\'ascension de Michael Corleone dans la mafia italienne.',
        3
    ),
    (
        43,
        'Michael Curtiz',
        1942,
        102,
        'Romance en temps de guerre dans le Maroc français.',
        1
    ),
    (
        44,
        'Orson Welles',
        1941,
        119,
        'L\'ascension et la chute d\'un magnat de la presse.',
        2
    ),
    (
        45,
        'Alfred Hitchcock',
        1958,
        128,
        'Un détective obsédé par une femme mystérieuse.',
        2
    ),
    (
        46,
        'Alfred Hitchcock',
        1960,
        109,
        'Norman Bates et son motel sinistre.',
        3
    ),
    (
        47,
        'Francis Ford Coppola',
        1979,
        147,
        'La guerre du Vietnam et la folie qu\'elle engendre.',
        3
    ),
    (
        48,
        'Stanley Kubrick',
        1968,
        149,
        'L\'évolution de l\'humanité et l\'intelligence artificielle.',
        2
    ),
    (
        49,
        'Martin Scorsese',
        1976,
        114,
        'Travis Bickle, chauffeur de taxi solitaire à New York.',
        3
    ),
    (
        50,
        'Martin Scorsese',
        1990,
        146,
        'Henry Hill et sa vie dans la mafia new-yorkaise.',
        3
    ),
    (
        51,
        'Stanley Kubrick',
        1980,
        146,
        'Jack Torrance sombre dans la folie dans un hôtel isolé.',
        3
    ),
    (
        52,
        'Ridley Scott',
        1979,
        117,
        'L\'équipage du Nostromo face à un alien mortel.',
        3
    ),
    (
        53,
        'Ridley Scott',
        1982,
        117,
        'Rick Deckard chasse les réplicants dans un futur dystopique.',
        2
    ),
    (
        54,
        'James Cameron',
        1991,
        137,
        'Un cyborg protège John Connor du futur.',
        2
    ),
    (
        55,
        'Lana et Lilly Wachowski',
        1999,
        136,
        'Neo découvre que la réalité est une simulation.',
        2
    ),
    (
        56,
        'David Fincher',
        1999,
        139,
        'Un homme rejoint un club de combat clandestin.',
        3
    ),
    (
        57,
        'David Fincher',
        1995,
        127,
        'Deux détectives traquent un tueur inspiré par les péchés capitaux.',
        3
    ),
    (
        58,
        'Jonathan Demme',
        1991,
        118,
        'Clarice Starling demande l\'aide d\'Hannibal Lecter.',
        3
    ),
    (
        59,
        'Robert Zemeckis',
        1994,
        142,
        'L\'histoire extraordinaire de Forrest Gump.',
        1
    ),
    (
        60,
        'Andrew Adamson',
        2001,
        90,
        'Un ogre vert part sauver une princesse.',
        1
    );

-- === 8. Insertion dans la table games ===
INSERT INTO
    games (
        media_id,
        publisher,
        platform_id,
        minimum_age,
        description,
        year
    )
VALUES (
        4,
        'Naughty Dog',
        3,
        16,
        'Survie dans un monde post-apocalyptique.',
        2020
    ),
    (
        16,
        'FromSoftware',
        1,
        16,
        'Un RPG d''action massivement ouvert dans un monde fantastique sombre.',
        2022
    ),
    (
        17,
        'Santa Monica Studio',
        1,
        16,
        'Kratos et son fils affrontent les dieux nordiques.',
        2022
    ),
    (
        18,
        'ZA/UM',
        4,
        16,
        'Un RPG narratif sur un détective amnésique dans une ville dystopique.',
        2019
    ),
    (
        19,
        'CD Projekt Red',
        4,
        16,
        'Un RPG futuriste à Night City, centré sur l''identité et la technologie.',
        2020
    ),
    (
        20,
        'Team Cherry',
        3,
        12,
        'Un jeu d''exploration dans un royaume souterrain inquiétant.',
        2017
    );

INSERT INTO
    games (
        media_id,
        publisher,
        platform_id,
        minimum_age,
        description,
        year
    )
VALUES (
        61,
        'CD Projekt Red',
        4,
        16,
        'Geralt de Riv dans un monde ouvert fantastique.',
        2015
    ),
    (
        62,
        'Rockstar Games',
        1,
        16,
        'Arthur Morgan dans l\'Ouest américain.',
        2018
    ),
    (
        63,
        'Rockstar Games',
        4,
        16,
        'Trois criminels dans la ville de Los Santos.',
        2013
    ),
    (
        64,
        'Mojang',
        4,
        7,
        'Construisez et explorez dans un monde de blocs.',
        2011
    ),
    (
        65,
        'Nintendo',
        3,
        12,
        'Link explore Hyrule dans un monde ouvert.',
        2017
    ),
    (
        66,
        'Nintendo',
        3,
        7,
        'Mario voyage à travers différents royaumes.',
        2017
    ),
    (
        67,
        'FromSoftware',
        4,
        16,
        'RPG d\'action sombre et difficile.',
        2016
    ),
    (
        68,
        'Blizzard Entertainment',
        4,
        12,
        'Jeu de tir en équipe avec des héros uniques.',
        2016
    ),
    (
        69,
        'Activision',
        1,
        16,
        'Jeu de tir militaire moderne.',
        2019
    ),
    (
        70,
        'EA Sports',
        1,
        3,
        'Simulation de football avec les vraies équipes.',
        2022
    ),
    (
        71,
        'Ubisoft',
        1,
        16,
        'Eivor explore l\'Angleterre viking.',
        2020
    ),
    (
        72,
        'Sony Interactive',
        1,
        12,
        'Peter Parker protège New York.',
        2018
    ),
    (
        73,
        'Sony Interactive',
        1,
        16,
        'Aloy chasse les machines dans un monde post-apocalyptique.',
        2017
    ),
    (
        74,
        'Sony Interactive',
        1,
        16,
        'Nathan Drake dans sa dernière aventure.',
        2016
    ),
    (
        75,
        'FromSoftware',
        1,
        16,
        'Action-RPG gothique et terrifiant.',
        2015
    ),
    (
        76,
        'Atlus',
        1,
        16,
        'RPG japonais avec des Personas.',
        2016
    ),
    (
        77,
        'Sony Interactive',
        1,
        16,
        'Jin Sakai défend le Japon contre les Mongols.',
        2020
    ),
    (
        78,
        'Nintendo',
        3,
        3,
        'Simulation de vie sur une île déserte.',
        2020
    ),
    (
        79,
        'InnerSloth',
        4,
        10,
        'Trouvez l\'imposteur parmi l\'équipage.',
        2018
    ),
    (
        80,
        'Mediatonic',
        4,
        7,
        'Course d\'obstacles colorée et amusante.',
        2020
    );

-- === 9. Insertion dans la table loans ===
INSERT INTO
    loans (
        user_id,
        media_id,
        loan_date,
        due_date,
        status
    )
VALUES (
        2,
        1,
        '2025-04-01',
        '2025-04-15',
        'En cours'
    ),
    (
        3,
        3,
        '2025-04-05',
        '2025-04-19',
        'En retard'
    ),
    (
        2,
        5,
        '2025-03-28',
        '2025-04-11',
        'Retourné'
    ),
    (
        1,
        7,
        '2025-04-02',
        '2025-04-16',
        'En cours'
    ),
    (
        4,
        11,
        '2025-04-03',
        '2025-04-17',
        'En cours'
    ),
    (
        4,
        16,
        '2025-03-25',
        '2025-04-08',
        'Retourné'
    ),
    (
        5,
        2,
        '2025-04-06',
        '2025-04-20',
        'En cours'
    ),
    (
        6,
        13,
        '2025-03-30',
        '2025-04-13',
        'En retard'
    ),
    (
        6,
        19,
        '2025-04-01',
        '2025-04-15',
        'En cours'
    ),
    (
        8,
        6,
        '2025-04-04',
        '2025-04-18',
        'En cours'
    ),
    (
        8,
        14,
        '2025-03-27',
        '2025-04-10',
        'Retourné'
    ),
    (
        1,
        20,
        '2025-04-07',
        '2025-04-21',
        'En cours'
    );

INSERT INTO
    loans (
        user_id,
        media_id,
        loan_date,
        due_date,
        status
    )
VALUES (
        11,
        21,
        '2025-04-08',
        '2025-04-22',
        'En cours'
    ),
    (
        12,
        41,
        '2025-04-09',
        '2025-04-23',
        'En cours'
    ),
    (
        13,
        61,
        '2025-04-10',
        '2025-04-24',
        'En cours'
    ),
    (
        14,
        22,
        '2025-04-11',
        '2025-04-25',
        'En cours'
    ),
    (
        15,
        42,
        '2025-04-12',
        '2025-04-26',
        'En cours'
    ),
    (
        16,
        62,
        '2025-04-13',
        '2025-04-27',
        'En cours'
    ),
    (
        17,
        23,
        '2025-04-14',
        '2025-04-28',
        'En cours'
    ),
    (
        18,
        43,
        '2025-04-15',
        '2025-04-29',
        'En cours'
    ),
    (
        19,
        63,
        '2025-04-16',
        '2025-04-30',
        'En cours'
    ),
    (
        20,
        24,
        '2025-04-17',
        '2025-05-01',
        'En cours'
    ),
    (
        11,
        44,
        '2025-03-20',
        '2025-04-03',
        'Retourné'
    ),
    (
        12,
        64,
        '2025-03-21',
        '2025-04-04',
        'En retard'
    ),
    (
        13,
        25,
        '2025-03-22',
        '2025-04-05',
        'Retourné'
    ),
    (
        14,
        45,
        '2025-03-23',
        '2025-04-06',
        'En retard'
    ),
    (
        15,
        65,
        '2025-03-24',
        '2025-04-07',
        'Retourné'
    ),
    (
        16,
        26,
        '2025-03-25',
        '2025-04-08',
        'En cours'
    ),
    (
        17,
        46,
        '2025-03-26',
        '2025-04-09',
        'En cours'
    ),
    (
        18,
        66,
        '2025-03-27',
        '2025-04-10',
        'En retard'
    ),
    (
        19,
        27,
        '2025-03-28',
        '2025-04-11',
        'Retourné'
    ),
    (
        20,
        47,
        '2025-03-29',
        '2025-04-12',
        'En cours'
    ),
    (
        1,
        67,
        '2025-04-01',
        '2025-04-15',
        'En cours'
    ),
    (
        2,
        28,
        '2025-04-02',
        '2025-04-16',
        'En cours'
    ),
    (
        3,
        48,
        '2025-04-03',
        '2025-04-17',
        'En retard'
    ),
    (
        4,
        68,
        '2025-04-04',
        '2025-04-18',
        'En cours'
    ),
    (
        5,
        29,
        '2025-04-05',
        '2025-04-19',
        'En cours'
    ),
    (
        6,
        49,
        '2025-04-06',
        '2025-04-20',
        'Retourné'
    ),
    (
        7,
        69,
        '2025-04-07',
        '2025-04-21',
        'En cours'
    ),
    (
        8,
        30,
        '2025-04-08',
        '2025-04-22',
        'En cours'
    ),
    (
        9,
        50,
        '2025-04-09',
        '2025-04-23',
        'En retard'
    ),
    (
        10,
        70,
        '2025-04-10',
        '2025-04-24',
        'En cours'
    );