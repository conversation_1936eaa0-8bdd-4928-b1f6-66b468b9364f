<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
        <a href="<?php echo url('admin'); ?>" class="back-link"><- Retour à l'interface d'administration</a>
    </div>
</div>

<section class="content">
    <div class="container">

        <!-- Barre d'actions -->
        <div class="actions-bar">
            <a href="<?php echo url('admin/add_media'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un média
            </a>
        </div>

        <!-- Filtres et recherche -->
        <div class="filters-section">
            <form method="GET" action="<?php echo url('admin/medias'); ?>" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="Rechercher un titre..."
                        value="<?php echo e($search); ?>" class="form-control">
                </div>

                <div class="filter-group">
                    <select name="type" class="form-control">
                        <option value="">Tous les types</option>
                        <option value="book" <?php echo $selected_type === 'book' ? 'selected' : ''; ?>>Livres</option>
                        <option value="film" <?php echo $selected_type === 'film' ? 'selected' : ''; ?>>Films</option>
                        <option value="game" <?php echo $selected_type === 'game' ? 'selected' : ''; ?>>Jeux</option>
                    </select>
                </div>

                <div class="filter-group">
                    <select name="genre" class="form-control">
                        <option value="">Tous les genres</option>
                        <?php foreach ($genres as $genre): ?>
                            <option value="<?php echo e($genre['id']); ?>"
                                <?php echo $selected_genre == $genre['id'] ? 'selected' : ''; ?>>
                                <?php e($genre['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" class="btn btn-secondary">Filtrer</button>
                <a href="<?php echo url('admin/medias'); ?>" class="btn btn-outline">Réinitialiser</a>
            </form>
        </div>

        <!-- Statistiques -->
        <div class="stats-summary">
            <span>Total: <?php echo e($total_medias); ?> média(s)</span>
        </div>

        <!-- Liste des médias -->
        <div class="medias-section">
            <?php if (!empty($medias)): ?>
                <div class="table-responsive">
                    <table class="medias-table">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Type</th>
                                <th>Genre</th>
                                <th>Stock</th>
                                <th>Ajouté le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($medias as $media): ?>
                                <tr>
                                    <td>
                                        <strong><?php e($media['title']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="media-type <?php e($media['media_type']); ?>">
                                            <?php
                                            switch ($media['media_type']) {
                                                case 'book':
                                                    echo 'Livre';
                                                    break;
                                                case 'film':
                                                    echo 'Film';
                                                    break;
                                                case 'game':
                                                    echo 'Jeu';
                                                    break;
                                                default:
                                                    echo ucfirst($media['media_type']);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php e($media['genre_name'] ?: 'Non défini'); ?></td>
                                    <td>
                                        <span class="stock-badge <?php echo $media['stock'] > 0 ? 'available' : 'unavailable'; ?>">
                                            <?php e($media['stock']); ?>
                                        </span>
                                    </td>
                                    <td><?php e(date('d/m/Y', strtotime($media['created_at']))); ?></td>
                                    <td class="actions">
                                        <a href="<?php echo url('admin/edit_media/' . $media['id']); ?>"
                                            class="btn btn-sm btn-outline" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo url('admin/delete_media/' . $media['id']); ?>"
                                            class="btn btn-sm btn-danger" title="Supprimer"
                                            data-action="delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($current_page > 1): ?>
                            <a href="<?php echo url('admin/medias?page=' . ($current_page - 1) .
                                            '&search=' . urlencode($search) .
                                            '&type=' . urlencode($selected_type) .
                                            '&genre=' . urlencode($selected_genre)); ?>"
                                class="btn btn-outline">Précédent</a>
                        <?php endif; ?>

                        <span class="page-info">
                            Page <?php echo e($current_page); ?> sur <?php echo e($total_pages); ?>
                        </span>

                        <?php if ($current_page < $total_pages): ?>
                            <a href="<?php echo url('admin/medias?page=' . ($current_page + 1) .
                                            '&search=' . urlencode($search) .
                                            '&type=' . urlencode($selected_type) .
                                            '&genre=' . urlencode($selected_genre)); ?>"
                                class="btn btn-outline">Suivant</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

            <?php else: ?>
                <div class="empty-state">
                    <p>Aucun média trouvé.</p>
                    <a href="<?php echo url('admin/add_media'); ?>" class="btn btn-primary">Ajouter le premier média</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>