<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
        <a href="<?php echo url('admin/medias'); ?>" class="back-link"><- Retour à la gestion des médias</a>
    </div>
</div>

<section class="content">
    <div class="container">
        <div class="form-container">
            <form method="POST" action="<?php echo url('admin/edit_media/' . $media['id']); ?>" class="media-form">
                <input type="hidden" name="csrf_token" value="<?php echo csrf_token(); ?>">

                <!-- Champs communs -->
                <div class="form-section">
                    <h3>Informations générales</h3>

                    <div class="form-group">
                        <label for="title">Titre <span class="required">*</span></label>
                        <input type="text" id="title" name="title" class="form-control"
                            value="<?php echo e(post('title', $media['title'])); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="media_type">Type de média</label>
                        <input type="text" class="form-control" value="<?php
                        switch ($media['media_type']) {
                            case 'book':
                                echo 'Livre';
                                break;
                            case 'film':
                                echo 'Film';
                                break;
                            case 'game':
                                echo 'Jeu';
                                break;
                            default:
                                echo ucfirst($media['media_type']);
                        }
                        ?>" readonly>
                        <small class="form-text">Le type de média ne peut pas être modifié</small>
                    </div>

                    <div class="form-group">
                        <label for="genre_id">Genre</label>
                        <select id="genre_id" name="genre_id" class="form-control">
                            <option value="">Sélectionner un genre</option>
                            <?php foreach ($genres as $genre): ?>
                                <option value="<?php echo e($genre['id']); ?>"
                                    <?php echo (post('genre_id') ?: $media['genre_id']) == $genre['id'] ? 'selected' : ''; ?>>
                                    <?php echo e($genre['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="stock">Stock <span class="required">*</span></label>
                        <input type="number" id="stock" name="stock" class="form-control"
                            value="<?php echo e(post('stock', $media['stock'])); ?>" min="1" required>
                    </div>

                    <div class="form-group">
                        <label for="cover">Couverture <span class="required">*</span></label>
                        <input type="text" id="cover" name="cover" class="form-control"
                            value="<?php echo e(post('cover', $media['cover'])); ?>" required>
                    </div>
                </div>

                <!-- Champs spécifiques aux livres -->
                <?php if ($media['media_type'] === 'book'): ?>
                    <div class="form-section">
                        <h3>Informations du livre</h3>

                        <div class="form-group">
                            <label for="author">Auteur <span class="required">*</span></label>
                            <input type="text" id="author" name="author" class="form-control"
                                value="<?php e(post('author', $media['author'] ?? '')); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="isbn">ISBN <span class="required">*</span></label>
                            <input type="text" id="isbn" name="isbn" class="form-control"
                                value="<?php e(post('isbn', $media['isbn'] ?? '')); ?>"
                                placeholder="Ex: 9782266123456" required>
                        </div>

                        <div class="form-group">
                            <label for="pages">Nombre de pages</label>
                            <input type="number" id="pages" name="pages" class="form-control"
                                value="<?php e(post('pages', $media['pages'] ?? '')); ?>" min="1">
                        </div>

                        <div class="form-group">
                            <label for="year">Année de publication <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control"
                                value="<?php e(post('year', $media['year'] ?? '')); ?>"
                                min="1000" max="<?php echo date('Y'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="summary">Résumé</label>
                            <textarea id="summary" name="summary" class="form-control" rows="4"><?php e(post('summary', $media['summary'] ?? '')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Champs spécifiques aux films -->
                <?php if ($media['media_type'] === 'film'): ?>
                    <div class="form-section">
                        <h3>Informations du film</h3>

                        <div class="form-group">
                            <label for="director">Réalisateur <span class="required">*</span></label>
                            <input type="text" id="director" name="director" class="form-control"
                                value="<?php e(post('director', $media['director'] ?? '')); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="year">Année de sortie <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control"
                                value="<?php e(post('year', $media['year'] ?? '')); ?>"
                                min="1890" max="<?php date('Y'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="duration">Durée (minutes)</label>
                            <input type="number" id="duration" name="duration" class="form-control"
                                value="<?php e(post('duration', $media['duration'] ?? '')); ?>" min="1">
                        </div>

                        <div class="form-group">
                            <label for="classification_id">Classification</label>
                            <select id="classification_id" name="classification_id" class="form-control">
                                <option value="">Sélectionner une classification</option>
                                <?php foreach ($classifications as $classification): ?>
                                    <option value="<?php e($classification['id']); ?>"
                                        <?php echo (post('classification_id') ?: $media['classification_id']) == $classification['id'] ? 'selected' : ''; ?>>
                                        <?php e($classification['name']); ?> - <?php e($classification['description']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="synopsis">Synopsis</label>
                            <textarea id="synopsis" name="synopsis" class="form-control" rows="4"><?php e(post('synopsis', $media['synopsis'] ?? '')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Champs spécifiques aux jeux -->
                <?php if ($media['media_type'] === 'game'): ?>
                    <div class="form-section">
                        <h3>Informations du jeu</h3>

                        <div class="form-group">
                            <label for="publisher">Éditeur <span class="required">*</span></label>
                            <input type="text" id="publisher" name="publisher" class="form-control"
                                value="<?php echo e(post('publisher', $media['publisher'] ?? '')); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="platform_id">Plateforme</label>
                            <select id="platform_id" name="platform_id" class="form-control">
                                <option value="">Sélectionner une plateforme</option>
                                <?php foreach ($platforms as $platform): ?>
                                    <option value="<?php e($platform['id']); ?>"
                                        <?php echo (post('platform_id') ?: $media['platform_id']) == $platform['id'] ? 'selected' : ''; ?>>
                                        <?php e($platform['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="minimum_age">Âge minimum</label>
                            <input type="number" id="minimum_age" name="minimum_age" class="form-control"
                                value="<?php e(post('minimum_age', $media['minimum_age'] ?? '')); ?>"
                                min="0" max="18">
                        </div>

                        <div class="form-group">
                            <label for="year">Année de sortie <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control"
                                value="<?php e(post('year', $media['year'] ?? '')); ?>"
                                min="1970" max="<?php echo date('Y'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control" rows="4"><?php e(post('description', $media['description'] ?? '')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Modifier le média</button>
                    <a href="<?php echo url('admin/medias'); ?>" class="btn btn-secondary">Annuler</a>
                </div>
            </form>
        </div>
    </div>
</section>