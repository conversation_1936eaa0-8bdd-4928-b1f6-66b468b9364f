<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
    </div>
</div>

<section class="content">
    <div class="container">
        <div class="content-flex">
            <div class="content-nav">
                <ul>
                    <a href="<?php echo url('admin/users'); ?>">
                        <li>Utilisateurs</li>
                    </a>
                    <a href="<?php echo url('admin/medias'); ?>">
                        <li>Medias</li>
                    </a>
                    <a href="<?php echo url('admin/dashboard'); ?>">
                        <li>Tableau de bord</li>
                    </a>
                </ul>
            </div>
            <div class="content-main">
                <table class="user-table">
                    <tr>
                        <th>ID</th>
                        <th>Prénom</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Emprunts en cours</th>
                        <th>Statistiques</th>
                    </tr>
                    <?php if (!empty($users)): ?>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo $user['id']; ?></a></td>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo $user['first_name']; ?></a></td>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo $user['last_name']; ?></a></td>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo $user['email']; ?></a></td>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></a></td>
                                <td><a href="<?php echo url('admin/user_details/' . $user['id']); ?>"><?php echo date('d/m/Y', strtotime($user['updated_at'])); ?></a></td>
                            <?php endforeach; ?>
                </table>

            <?php else: ?>
                <p>Aucun utilisateur</p>
            <?php endif; ?>
            </div>
        </div>
    </div>
</section>