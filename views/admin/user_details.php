<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
        <a href="<?php echo url('admin'); ?>" class="back-link"><- Retour à l'interface d'administration</a>
    </div>
</div>

<section class="content">
    <div class="container">
        <div class="user-details-grid">
            <!-- Informations personnelles de l'utilisateur (en forme de carte)-->
            <div class="user-info-card">
                <h3>Informations personnelles</h3>
                <p><strong>ID:</strong> <?php echo e($user['id']) ?> </p>
                <p><strong>Nom:</strong> <?php echo e($user['last_name'] . ' ' . $user['first_name']) ?> </p>
                <p><strong>Email:</strong> <?php echo e($user['email']) ?> </p>
                <p><strong>Inscrit le:</strong> <?php echo e(date('d/m/Y', strtotime($user['created_at']))) ?> </p>
            </div>
        </div>

        <!-- Statistiques de l'utilisateur -->
        <div class="stats-card">
            <h3>Statistiques</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number"> <?php echo e($stats['total_loans']); ?></span>
                    <span class="stat-label">Total emprunts</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($stats['active_loans']); ?></span>
                    <span class="stat-label">Emprunts en cours</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($stats['overdue_loans']); ?></span>
                    <span class="stat-label">Emprunts en retard</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">Type préféré:</span>
                    <span class="stat-label"><?php echo e($stats['favorite_type']); ?></span>
                </div>
            </div>
        </div>

        <!-- Emprents en cours -->
        <div class="loans-section">
            <h3>Emprents en cours (<?php echo count($active_loans); ?>)</h3>
            <?php if (!empty($active_loans)): ?>
                <table class="loans-table">
                    <thead>
                        <tr>
                            <th>Média</th>
                            <th>Type</th>
                            <th>Date d'emprunt</th>
                            <th>Date de retour</th>
                            <th>Statut</th>
                            <th>Rendre</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($active_loans as $loan): ?>
                            <tr>
                                <td><?= e($loan['media_title']); ?></td>
                                <td><?= e($loan['media_type']); ?></td>
                                <td><?= e(date('d/m/Y', strtotime($loan['loan_date']))); ?></td>
                                <td><?= e(date('d/m/Y', strtotime($loan['due_date']))); ?></td>
                                <td>
                                    <span class="status <?php echo strtolower($loan['status']); ?>">
                                        <?php echo e($loan['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($loan['status'] == 'En cours'): ?>
                                        <a href="<?php echo url('admin/return_loan/' . $loan['id']); ?>">Rendre</a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>Aucun emprunt en cours</p>
            <?php endif; ?>

            <!-- Historique des emprunts -->
            <h3>Historique des emprunts</h3>
            <?php if (!empty($loan_history)): ?>
                <table class="loans-table">
                    <thead>
                        <tr>
                            <th>Média</th>
                            <th>Type</th>
                            <th>Date d'emprunt</th>
                            <th>Date de retour</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($loan_history as $loan): ?>
                            <tr>
                                <td><?= e($loan['media_title']); ?></td>
                                <td><?= e($loan['media_type']); ?></td>
                                <td><?= e(date('d/m/Y', strtotime($loan['loan_date']))); ?></td>
                                <td><?= e(date('d/m/Y', strtotime($loan['due_date']))); ?></td>
                                <td>
                                    <span class="status <?php echo strtolower($loan['status']); ?>">
                                        <?php echo e($loan['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>Aucun emprunt dans l'historique</p>
            <?php endif; ?>
        </div>
    </div>
</section>