<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
        <a href="<?php echo url('admin/medias'); ?>" class="back-link"><- Retour à la gestion des médias</a>
    </div>
</div>

<section class="content">
    <div class="container">
        <div class="confirmation-container">
            <div class="warning-box">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>

                <h2>Confirmation de suppression</h2>

                <div class="media-info">
                    <h3>Vous êtes sur le point de supprimer ce média :</h3>

                    <div class="media-details">
                        <p><strong>Titre :</strong> <?php echo e($media['title']); ?></p>
                        <p><strong>Type :</strong>
                            <?php
                            switch ($media['media_type']) {
                                case 'book':
                                    echo 'Livre';
                                    break;
                                case 'film':
                                    echo 'Film';
                                    break;
                                case 'game':
                                    echo 'Jeu';
                                    break;
                                default:
                                    echo ucfirst($media['media_type']);
                            }
                            ?>
                        </p>
                        <p><strong>Stock :</strong> <?php echo e($media['stock']); ?></p>

                        <?php if (isset($media['author'])): ?>
                            <p><strong>Auteur :</strong> <?php echo e($media['author']); ?></p>
                        <?php endif; ?>

                        <?php if (isset($media['director'])): ?>
                            <p><strong>Réalisateur :</strong> <?php echo e($media['director']); ?></p>
                        <?php endif; ?>

                        <?php if (isset($media['publisher'])): ?>
                            <p><strong>Éditeur :</strong> <?php echo e($media['publisher']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="warning-message">
                    <p><strong>⚠️ Attention :</strong> Cette action est irréversible !</p>
                    <p>Le média sera définitivement supprimé de la base de données.</p>
                    <p><em>Note : Si ce média est actuellement emprunté, la suppression sera impossible.</em></p>
                </div>

                <div class="confirmation-actions">
                    <form method="POST" action="<?php echo url('admin/delete_media/' . $media['id']); ?>" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo csrf_token(); ?>">
                        <input type="hidden" name="confirm" value="yes">
                        <button type="submit" class="btn btn-danger" data-action="delete">
                            <i class="fas fa-trash"></i> Oui, supprimer définitivement
                        </button>
                    </form>

                    <a href="<?php echo url('admin/medias'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Non, annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>