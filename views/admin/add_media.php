<div class="page-header">
    <div class="container">
        <h1><?php e($title); ?></h1>
        <a href="<?php echo url('admin/medias'); ?>" class="back-link"><- Retour à la gestion des médias</a>
    </div>
</div>

<section class="content">
    <div class="container">
        <div class="form-container">
            <form method="POST" action="<?php echo url('admin/add_media'); ?>" class="media-form">
                <input type="hidden" name="csrf_token" value="<?php echo csrf_token(); ?>">

                <!-- Champs communs -->
                <div class="form-section">
                    <h3>Informations générales</h3>

                    <div class="form-group">
                        <label for="title">Titre <span class="required">*</span></label>
                        <input type="text" id="title" name="title" class="form-control"
                            value="<?php e(post('title')); ?>">
                    </div>

                    <div class="form-group">
                        <label for="media_type">Type de média <span class="required">*</span></label>
                        <div style="display: flex; gap: 10px; align-items: flex-end;">
                            <select id="media_type" name="media_type" class="form-control" style="flex: 1;">
                                <option value="">Sélectionner un type</option>
                                <option value="book" <?php e(post('media_type') === 'book' ? 'selected' : ''); ?>>Livre</option>
                                <option value="film" <?php e(post('media_type') === 'film' ? 'selected' : ''); ?>>Film</option>
                                <option value="game" <?php e(post('media_type') === 'game' ? 'selected' : ''); ?>>Jeu</option>
                            </select>
                            <button type="submit" name="select_type" class="btn btn-secondary">Sélectionner</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="genre">Genre <span class="required">*</span></label>
                        <select id="genre" name="genre" class="form-control">
                            <option value="">Sélectionner un genre</option>
                            <?php foreach ($genres as $genre): ?>
                                <option value="<?php e($genre['id']); ?>"
                                    <?php e(post('genre') == $genre['id'] ? 'selected' : ''); ?>>
                                    <?php e($genre['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="stock">Stock <span class="required">*</span></label>
                        <input type="number" id="stock" name="stock" class="form-control"
                            value="<?php e(post('stock', 1)); ?>" min="1">
                    </div>

                    <div class="form-group">
                        <label for="cover">Couverture <span class="required">*</span></label>
                        <input type="text" id="cover" name="cover" class="form-control"
                            value="<?php e(post('cover')); ?>">
                    </div>
                </div>

                <!-- Champs spécifiques selon le type de média -->
                <?php $selected_media_type = post('media_type'); ?>

                <?php if ($selected_media_type === 'book'): ?>
                    <!-- Champs spécifiques aux livres -->
                    <div class="form-section">
                        <h3>Informations du livre</h3>

                        <div class="form-group">
                            <label for="author">Auteur <span class="required">*</span></label>
                            <input type="text" id="author" name="author" class="form-control"
                                value="<?php e(post('author')); ?>">
                        </div>

                        <div class="form-group">
                            <label for="isbn">ISBN <span class="required">*</span></label>
                            <input type="text" id="isbn" name="isbn" class="form-control"
                                value="<?php e(post('isbn')); ?>"
                                placeholder="Ex: 9782266123456">
                        </div>

                        <div class="form-group">
                            <label for="pages">Nombre de pages <span class="required">*</span></label>
                            <input type="number" id="pages" name="pages" class="form-control"
                                value="<?php e(post('pages')); ?>" min="1">
                        </div>

                        <div class="form-group">
                            <label for="year">Année de publication <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control" required
                                value="<?php e(post('year')); ?>" min="1000" max="<?php echo date('Y'); ?>"
                                title="Veuillez entrer une année entre 1000 et <?php echo date('Y'); ?>">
                        </div>

                        <div class="form-group">
                            <label for="summary">Résumé <span class="required">*</span></label>
                            <textarea id="summary" name="summary" class="form-control" rows="4"><?php e(post('summary')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($selected_media_type === 'film'): ?>
                    <!-- Champs spécifiques aux films -->
                    <div class="form-section">
                        <h3>Informations du film</h3>

                        <div class="form-group">
                            <label for="director">Réalisateur <span class="required">*</span></label>
                            <input type="text" id="director" name="director" class="form-control"
                                value="<?php e(post('director')); ?>">
                        </div>

                        <div class="form-group">
                            <label for="year">Année de sortie <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control" required
                                value="<?php e(post('year')); ?>" min="1890" max="<?php echo date('Y'); ?>"
                                title="Veuillez entrer une année entre 1890 et <?php echo date('Y'); ?>">
                        </div>

                        <div class="form-group">
                            <label for="duration">Durée (minutes) <span class="required">*</span></label>
                            <input type="number" id="duration" name="duration" class="form-control"
                                value="<?php e(post('duration')); ?>" min="1">
                        </div>

                        <div class="form-group">
                            <label for="classification_id">Classification <span class="required">*</span></label>
                            <select id="classification_id" name="classification_id" class="form-control">
                                <option value="">Sélectionner une classification</option>
                                <?php foreach ($classifications as $classification): ?>
                                    <option value="<?php e($classification['id']); ?>"
                                        <?php e(post('classification_id') == $classification['id'] ? 'selected' : ''); ?>>
                                        <?php e($classification['name']); ?> - <?php e($classification['description']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="synopsis">Synopsis <span class="required">*</span></label>
                            <textarea id="synopsis" name="synopsis" class="form-control" rows="4"><?php e(post('synopsis')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($selected_media_type === 'game'): ?>
                    <!-- Champs spécifiques aux jeux -->
                    <div class="form-section">
                        <h3>Informations du jeu</h3>

                        <div class="form-group">
                            <label for="publisher">Éditeur <span class="required">*</span></label>
                            <input type="text" id="publisher" name="publisher" class="form-control"
                                value="<?php e(post('publisher')); ?>">
                        </div>

                        <div class="form-group">
                            <label for="platform_id">Plateforme <span class="required">*</span></label>
                            <select id="platform_id" name="platform_id" class="form-control">
                                <option value="">Sélectionner une plateforme</option>
                                <?php foreach ($platforms as $platform): ?>
                                    <option value="<?php e($platform['id']); ?>"
                                        <?php e(post('platform_id') == $platform['id'] ? 'selected' : ''); ?>>
                                        <?php e($platform['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="minimum_age">Âge minimum <span class="required">*</span></label>
                            <input type="number" id="minimum_age" name="minimum_age" class="form-control"
                                value="<?php e(post('minimum_age')); ?>" min="0" max="18">
                        </div>

                        <div class="form-group">
                            <label for="year">Année de sortie <span class="required">*</span></label>
                            <input type="number" id="year" name="year" class="form-control" required
                                value="<?php e(post('year')); ?>" min="1970" max="<?php echo date('Y'); ?>"
                                title="Veuillez entrer une année entre 1970 et <?php echo date('Y'); ?>">
                        </div>

                        <div class="form-group">
                            <label for="description">Description <span class="required">*</span></label>
                            <textarea id="description" name="description" class="form-control" rows="4"><?php e(post('description')); ?></textarea>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Ajouter le média</button>
                    <a href="<?php echo url('admin/medias'); ?>" class="btn btn-secondary">Annuler</a>
                </div>
            </form>
        </div>
    </div>
</section>