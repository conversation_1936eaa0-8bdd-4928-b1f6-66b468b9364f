

<section class="search-section">
  <div class="container">

    <form action="<?php echo url('catalog/search'); ?>" method="GET">

      <div class="search-bar">
      <input type="text" name="titre" placeholder="Rechercher un titre, un auteur..." value="<?= htmlspecialchars($selected_title) ?>" 
/>
      <button type="submit">Rechercher</button>
      </div>

  <div class="filters">
  <select name="type">
  <option value="">Type de média</option>
  <option value="book" <?= ($selected_type === "book") ? "selected" : "" ?>>Livre</option>
  <option value="film" <?= ($selected_type === "film") ? "selected" : "" ?>>Film</option>
  <option value="game" <?= ($selected_type === "game") ? "selected" : "" ?>>Jeu vidéo</option>
</select>

<select name="genre">
  <option value="">Genre</option>
  <option value="action" <?= ($selected_genre === "action") ? "selected" : "" ?>>Action</option>
  <option value="aventure" <?= ($selected_genre === "aventure") ? "selected" : "" ?>>Aventure</option>
  <option value="drame" <?= ($selected_genre === "drame") ? "selected" : "" ?>>Drame</option>
  <option value="science-fiction" <?= ($selected_genre === "science-fiction") ? "selected" : "" ?>>Science-fiction</option>
  <option value="fantastique" <?= ($selected_genre === "fantastique") ? "selected" : "" ?>>Fantastique</option>
</select>

<select name="disponibilite">
  <option value="">Disponibilité</option>
  <option value="disponible" <?= ($selected_disponibilite === "disponible") ? "selected" : "" ?>>Disponible</option>
  <option value="emprunte" <?= ($selected_disponibilite === "emprunte") ? "selected" : "" ?>>Emprunté</option>
</select>
        
      </div>
    </form>
  </div>
</section>


<div>
  <ul class="catalog-container">

<?php foreach ($medias as $media): ?>
<li class="card">
  <a href="<?= url('catalog/detail?id=' . $media['media_id']) ?>">
    <img 
      src="<?= url("uploads/covers/" . $media['images']); ?>" 
      alt="<?= htmlspecialchars($media['title']) ?>" 
      width="150" 
      height="200" 
      class="media-image"
    />
        <p class="card-title">
          <strong><?= htmlspecialchars($media['title']) ?></strong>
        </p>
        <span class="card-info"><?= htmlspecialchars($media['media_type'] ?? '') ?></span>
        <span class="card-info"><?= htmlspecialchars($media['author'] ?? '') ?></span>
        <span class="card-info"><?= htmlspecialchars($media['director'] ?? '') ?></span>
        <span class="card-info"><?= htmlspecialchars($media['publisher'] ?? '') ?></span>
      </li>
    <?php endforeach; ?>
  </ul>
</div>



