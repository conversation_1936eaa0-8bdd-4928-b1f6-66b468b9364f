<?php

$mediaId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$media) {
    echo "Média non trouvé.";
    exit;
}
?>


<div class="media-detail-container">
  <h1><?= htmlspecialchars($media['title']) ?></h1>

  <?php if (!empty($media['images'])): ?>
    <img src="<?= url('uploads/covers/' . $media['images']) ?>" alt="<?= htmlspecialchars($media['title']) ?>" />
  <?php endif; ?>

  <p><strong>Type :</strong> <?= htmlspecialchars($media['media_type'] ?? '') ?></p>
  <?php if (!empty($media['author'])): ?>
    <p><strong>Auteur :</strong> <?= htmlspecialchars($media['author']) ?></p>
  <?php endif; ?>
  <?php if (!empty($media['director'])): ?>
    <p><strong>Réalisateur :</strong> <?= htmlspecialchars($media['director']) ?></p>
  <?php endif; ?>
  <?php if (!empty($media['publisher'])): ?>
    <p><strong>Éditeur :</strong> <?= htmlspecialchars($media['publisher']) ?></p>
  <?php endif; ?>

  <a href="<?= url('catalog/search') 
    . '?titre=' . urlencode($search) 
    . '&type=' . urlencode($filter_type) 
    . '&genre=' . urlencode($filter_genre) 
    . '&disponibilite=' . urlencode($filter_disponibilite) ?>">
    &larr; Retour au catalogue
</a>
</div>





