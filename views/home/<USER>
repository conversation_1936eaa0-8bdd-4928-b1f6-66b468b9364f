<!-- Creation du formulaire d'inscription : 
add_inscription_Nicolas.php -->

<h2>Inscription</h2>

<?php if (!empty($erreurs)): ?>
    <ul style="color:red;">
        <?php foreach ($erreurs as $erreur): ?>
            <li><?= htmlspecialchars($erreur) ?></li>
        <?php endforeach; ?>
    </ul>
<?php endif; ?>

<form method="POST" action="/inscription">
    <label>Nom :
        <input type="text" name="nom" required minlength="2" maxlength="50" pattern="[A-Za-zÀ-ÿ\- ]+" title="Lettres, espaces ou tirets uniquement">
    </label><br><br>

    <label>Prénom :
        <input type="text" name="prenom" required minlength="2" maxlength="50" pattern="[A-Za-zÀ-ÿ\- ]+" title="Lettres, espaces ou tirets uniquement">
    </label><br><br>

    <label>Email :
        <input type="email" name="email" required maxlength="100">
    </label><br><br>

    <label>Mot de passe :
        <input type="password" name="mot_de_passe" required minlength="8" pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}" 
        title="Au moins 1 minuscule, 1 majuscule, 1 chiffre, 8 caractères minimum">
    </label><br><br>

    <label>Confirmer le mot de passe :
        <input type="password" name="confirmation_mot_de_passe" required>
    </label><br><br>

    <button type="submit">S'inscrire</button>
</form>
